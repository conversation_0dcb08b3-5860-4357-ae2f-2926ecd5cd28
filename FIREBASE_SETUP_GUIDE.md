# 🔥 Firebase Configuration Guide
## Barber Brothers Legacy - Complete Setup Instructions

### 📋 Overview
This guide will help you properly configure Firebase for the Barber Brothers Legacy website, including Authentication, Firestore Database, Storage, and Analytics.

---

## 🚀 Step 1: Firebase Project Setup

### 1.1 Create Firebase Project
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project" or "Add project"
3. Enter project name: **"Barber Brothers Legacy"**
4. Enable Google Analytics (recommended)
5. Choose or create a Google Analytics account
6. Click "Create project"

### 1.2 Project Configuration
1. In the Firebase Console, go to **Project Settings** (gear icon)
2. In the "General" tab, scroll down to "Your apps"
3. Click "Add app" and select the **Web** platform (`</>`)
4. Enter app nickname: **"Barber Brothers Website"**
5. Check "Also set up Firebase Hosting" (optional)
6. Click "Register app"

---

## 🔧 Step 2: Get Firebase Configuration

### 2.1 Copy Configuration Values
After registering your app, you'll see a configuration object like this:

```javascript
const firebaseConfig = {
  apiKey: "AIzaSyAHqpO8PPXmZIfype7dsViz3chKcmLdmpY",
  authDomain: "barber-brothers-legacy.firebaseapp.com",
  projectId: "barber-brothers-legacy",
  storageBucket: "barber-brothers-legacy.appspot.com",
  messagingSenderId: "************",
  appId: "1:************:web:d65f5bef7973127d1abc67",
  measurementId: "G-XXXXXXXXXX"
};
```

### 2.2 Update Environment Variables
1. Copy `.env.example` to `.env`
2. Update the Firebase configuration values:

```bash
# Firebase Configuration
REACT_APP_FIREBASE_API_KEY=your-actual-api-key
REACT_APP_FIREBASE_AUTH_DOMAIN=barber-brothers-legacy.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=barber-brothers-legacy
REACT_APP_FIREBASE_STORAGE_BUCKET=barber-brothers-legacy.appspot.com
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=your-actual-sender-id
REACT_APP_FIREBASE_APP_ID=your-actual-app-id
REACT_APP_FIREBASE_MEASUREMENT_ID=your-actual-measurement-id
```

---

## 🔐 Step 3: Configure Authentication

### 3.1 Enable Authentication
1. In Firebase Console, go to **Authentication**
2. Click "Get started"
3. Go to the **Sign-in method** tab

### 3.2 Enable Google Sign-In
1. Click on **Google** provider
2. Toggle "Enable"
3. Enter your project support email
4. Add authorized domains:
   - `localhost` (for development)
   - `barberbrotherz.com`
   - `www.barberbrotherz.com`
5. Click "Save"

### 3.3 Configure OAuth Settings
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your Firebase project
3. Go to **APIs & Services** > **Credentials**
4. Find your OAuth 2.0 client ID
5. Add authorized redirect URIs:
   - `https://www.barberbrotherz.com/auth-callback.html`
   - `https://barberbrotherz.com/auth-callback.html`
   - `http://localhost:3000/auth-callback.html` (for development)

---

## 📊 Step 4: Configure Firestore Database

### 4.1 Create Firestore Database
1. In Firebase Console, go to **Firestore Database**
2. Click "Create database"
3. Choose **Start in test mode** (for now)
4. Select a location (choose closest to your users)
5. Click "Done"

### 4.2 Set Up Security Rules
Replace the default rules with these production-ready rules:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Posts are readable by all, writable by authenticated users
    match /posts/{postId} {
      allow read: if true;
      allow create: if request.auth != null;
      allow update, delete: if request.auth != null && 
        (request.auth.uid == resource.data.authorId || 
         request.auth.token.admin == true);
    }
    
    // Comments are readable by all, writable by authenticated users
    match /comments/{commentId} {
      allow read: if true;
      allow create: if request.auth != null;
      allow update, delete: if request.auth != null && 
        request.auth.uid == resource.data.authorId;
    }
    
    // Appointments - users can only access their own
    match /appointments/{appointmentId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.userId;
    }
    
    // Admin-only collections
    match /admin/{document=**} {
      allow read, write: if request.auth != null && 
        request.auth.token.admin == true;
    }
  }
}
```

---

## 📁 Step 5: Configure Storage

### 5.1 Enable Storage
1. In Firebase Console, go to **Storage**
2. Click "Get started"
3. Choose **Start in test mode**
4. Select the same location as your Firestore database
5. Click "Done"

### 5.2 Set Up Storage Rules
Replace the default rules with these:

```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // User profile images
    match /users/{userId}/profile/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Post images
    match /posts/{postId}/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // Community uploads
    match /community/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // Admin uploads
    match /admin/{allPaths=**} {
      allow read, write: if request.auth != null && 
        request.auth.token.admin == true;
    }
  }
}
```

---

## 📈 Step 6: Configure Analytics (Optional)

### 6.1 Enable Analytics
1. In Firebase Console, go to **Analytics**
2. If not already enabled, click "Enable Analytics"
3. Configure your analytics settings
4. Note your Measurement ID (starts with G-)

### 6.2 Update Configuration
Add your Measurement ID to your environment variables:
```bash
REACT_APP_FIREBASE_MEASUREMENT_ID=G-XXXXXXXXXX
```

---

## 🔧 Step 7: Development Setup

### 7.1 Install Firebase CLI (Optional)
```bash
npm install -g firebase-tools
firebase login
firebase init
```

### 7.2 Local Development
For local development, you can use Firebase emulators:

```bash
# Install emulators
firebase init emulators

# Start emulators
firebase emulators:start
```

Update your `.env` for development:
```bash
REACT_APP_USE_FIREBASE_EMULATORS=true
NODE_ENV=development
```

---

## ✅ Step 8: Testing Your Setup

### 8.1 Test Authentication
1. Open your website
2. Try signing in with Google
3. Check the browser console for any errors
4. Verify user appears in Firebase Console > Authentication > Users

### 8.2 Test Database
1. Try creating a post or comment
2. Check Firebase Console > Firestore Database
3. Verify data appears correctly

### 8.3 Test Storage
1. Try uploading an image
2. Check Firebase Console > Storage
3. Verify file appears in the correct folder

---

## 🚨 Security Checklist

- [ ] Updated Firestore security rules
- [ ] Updated Storage security rules
- [ ] Added authorized domains to Authentication
- [ ] Added redirect URIs to Google OAuth
- [ ] Environment variables are properly set
- [ ] API keys are not exposed in client-side code
- [ ] Test mode is disabled in production

---

## 🆘 Troubleshooting

### Common Issues:

1. **"Firebase: Error (auth/unauthorized-domain)"**
   - Add your domain to authorized domains in Authentication settings

2. **"Missing or insufficient permissions"**
   - Check your Firestore security rules
   - Ensure user is properly authenticated

3. **"Storage upload failed"**
   - Check Storage security rules
   - Verify file size limits

4. **"Analytics not working"**
   - Ensure Measurement ID is correct
   - Check if analytics is enabled for your project

### Getting Help:
- Check browser console for detailed error messages
- Review Firebase Console logs
- Consult [Firebase Documentation](https://firebase.google.com/docs)

---

## 📝 Next Steps

After completing this setup:
1. Test all functionality thoroughly
2. Set up monitoring and alerts
3. Configure backup strategies
4. Plan for scaling and optimization
5. Review security settings regularly

Your Firebase configuration should now be complete and ready for production use!
