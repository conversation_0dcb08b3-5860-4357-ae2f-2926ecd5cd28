// Simple Node.js server to handle appointment bookings and Twilio SMS notifications
const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const path = require('path');
require('dotenv').config();
const twilio = require('twilio');
const googleOAuthRouter = require('./server/google-oauth');

const app = express();
app.use(cors());
app.use(express.json());

// Set to true to bypass <PERSON>wi<PERSON> for testing
const TEST_MODE = false;

// Initialize Twilio client with increased timeout (if not in test mode)
let client;
if (!TEST_MODE) {
    console.log('Initializing Twilio client with credentials:', {
        hasSid: !!process.env.TWILIO_ACCOUNT_SID,
        hasToken: !!process.env.TWILIO_AUTH_TOKEN,
        hasFromNumber: !!process.env.TWILIO_PHONE_NUMBER,
        hasBarberNumber: !!process.env.BARBER_PHONE_NUMBER
    });
    
    if (!process.env.TWILIO_ACCOUNT_SID || !process.env.TWILIO_AUTH_TOKEN) {
        throw new Error('Twilio credentials are not configured');
    }

    // Verify credentials in an async IIFE
    (async () => {
        try {
            const testClient = new twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
            const account = await testClient.api.accounts(process.env.TWILIO_ACCOUNT_SID).fetch();
            console.log('Twilio account verified:', account.friendlyName);
        } catch (error) {
            console.error('Twilio credential verification failed:', error);
            throw new Error('Invalid Twilio credentials - please check Account SID and Auth Token');
        }
    })();
    
    client = new twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN, {
        timeout: 60000  // 60 seconds timeout
    });
}

app.post('/send-sms-notification', async (req, res) => {
    try {
        const { name, phone, service, date, time } = req.body;
        const notes = req.body.notes || '';
        console.log('Received booking request:', { name, phone, service, date, time, notes });

        // Validate required fields
        if (!name || !phone || !service || !date || !time) {
            console.error('Missing required fields:', { name, phone, service, date, time });
            return res.status(400).json({
                success: false,
                message: 'Please fill in all required fields'
            });
        }

        // Format phone number to E.164 format
        let formattedPhone = phone;
        if (!formattedPhone.startsWith('+')) {
            // Remove all non-digit characters
            formattedPhone = formattedPhone.replace(/\D/g, '');
            // Add +1 prefix for US numbers
            if (formattedPhone.length === 10) {
                formattedPhone = `+1${formattedPhone}`;
            } else if (formattedPhone.length === 11 && formattedPhone.startsWith('1')) {
                formattedPhone = `+${formattedPhone}`;
            }
        }
        console.log('Formatted phone number:', formattedPhone);
        
        if (TEST_MODE) {
            // Skip actual Twilio calls in test mode
            console.log('TEST MODE: Simulating successful SMS delivery');
            console.log(`TEST MODE: Would have sent confirmation to customer: ${formattedPhone}`);
            console.log(`TEST MODE: Would have sent notification to barber`);
            
            // Return success response
            return res.json({ 
                success: true,
                message: 'Appointment booked successfully! (TEST MODE)',
                testMode: true
            });
        }
        
        // This code only runs if not in test mode
        // Send SMS to customer
        const customerMessage = await client.messages.create({
            body: `Hi ${name}, your appointment for ${service} has been confirmed for ${date} at ${time}. See you then! - Barber Brothers Legacy`,
            to: formattedPhone,
            from: process.env.TWILIO_PHONE_NUMBER
        }).catch(error => {
            console.error('Error sending customer SMS:', error);
            throw new Error('Failed to send confirmation SMS to customer');
        });

        console.log('Customer SMS sent:', customerMessage.sid);

        // Send SMS to barber
        const barberMessage = await client.messages.create({
            body: `New appointment: ${name} for ${service} on ${date} at ${time}. Customer phone: ${formattedPhone}${notes ? `\nNotes: ${notes}` : ''}`,
            to: process.env.BARBER_PHONE_NUMBER,
            from: process.env.TWILIO_PHONE_NUMBER
        }).catch(error => {
            console.error('Error sending barber SMS:', error);
            throw new Error('Failed to send notification to barber');
        });

        console.log('Barber SMS sent:', barberMessage.sid);

        res.json({ 
            success: true,
            message: 'Appointment booked successfully!'
        });
    } catch (error) {
        console.error('SMS Error:', error);
        console.log('Twilio credentials check:', {
            hasSid: !!process.env.TWILIO_ACCOUNT_SID,
            hasToken: !!process.env.TWILIO_AUTH_TOKEN,
            hasFromNumber: !!process.env.TWILIO_PHONE_NUMBER,
            hasBarberNumber: !!process.env.BARBER_PHONE_NUMBER
        });
        
        res.status(500).json({ 
            success: false, 
            message: 'Failed to send notification. Please try again or call us at (*************.',
            error: error.message
        });
    }
});

app.use(googleOAuthRouter);

// Voice Agent API Routes
const voiceAgentRouter = require('./server/voice-agent-api');
app.use('/api/voice-agent', voiceAgentRouter);

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => console.log(`Server running on port ${PORT}`));
