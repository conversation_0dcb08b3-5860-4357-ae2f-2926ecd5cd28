/**
 * Post Controller
 * Handles all post-related operations including CRUD, likes, and social features
 */

const Post = require('../models/Post');
const User = require('../models/User');
const Notification = require('../models/Notification');
const { validationResult } = require('express-validator');

class PostController {
    /**
     * Create a new post
     */
    static async createPost(req, res) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const { content, visibility = 'public', allowComments = true, location } = req.body;
            const author = req.user._id;

            // Process uploaded images
            const images = req.processedImages || [];

            // Create post
            const newPost = new Post({
                author,
                content,
                images,
                visibility,
                allowComments,
                location: location ? JSON.parse(location) : undefined
            });

            await newPost.save();

            // Populate author information
            await newPost.populate('author', 'username displayName avatar role isVerified');

            // Update user's post count
            await User.findByIdAndUpdate(author, { $inc: { postsCount: 1 } });

            // Emit real-time event
            const io = req.app.get('io');
            if (io && visibility === 'public') {
                io.emit('post-created', {
                    post: newPost,
                    author: newPost.author
                });
            }

            console.log(`✅ New post created by ${req.user.username}: ${newPost._id}`);

            res.status(201).json({
                success: true,
                message: 'Post created successfully',
                data: {
                    post: newPost
                }
            });

        } catch (error) {
            console.error('❌ Create post error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to create post',
                error: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    }

    /**
     * Get all posts with pagination and filtering
     */
    static async getPosts(req, res) {
        try {
            const {
                page = 1,
                limit = 20,
                sort = 'newest',
                tag,
                author,
                search
            } = req.query;

            const skip = (page - 1) * limit;
            const query = { visibility: 'public' };

            // Add filters
            if (tag) {
                query.tags = { $in: [tag] };
            }

            if (author) {
                const authorUser = await User.findOne({ username: author });
                if (authorUser) {
                    query.author = authorUser._id;
                }
            }

            if (search) {
                query.$or = [
                    { content: { $regex: search, $options: 'i' } },
                    { tags: { $in: [new RegExp(search, 'i')] } }
                ];
            }

            // Sort options
            let sortOption = {};
            switch (sort) {
                case 'newest':
                    sortOption = { createdAt: -1 };
                    break;
                case 'oldest':
                    sortOption = { createdAt: 1 };
                    break;
                case 'popular':
                    sortOption = { likesCount: -1, createdAt: -1 };
                    break;
                case 'trending':
                    // Calculate trending score based on likes and recency
                    sortOption = { trendingScore: -1, createdAt: -1 };
                    break;
                default:
                    sortOption = { createdAt: -1 };
            }

            const posts = await Post.find(query)
                .populate('author', 'username displayName avatar isVerified')
                .populate('likes', 'username displayName avatar')
                .sort(sortOption)
                .skip(skip)
                .limit(parseInt(limit))
                .lean();

            // Get total count for pagination
            const totalPosts = await Post.countDocuments(query);
            const totalPages = Math.ceil(totalPosts / limit);

            // Add user interaction data if authenticated
            if (req.user) {
                posts.forEach(post => {
                    post.isLiked = post.likes.some(like => like._id.toString() === req.user._id.toString());
                    post.isOwner = post.author._id.toString() === req.user._id.toString();
                });
            }

            res.json({
                success: true,
                data: {
                    posts,
                    pagination: {
                        currentPage: parseInt(page),
                        totalPages,
                        totalPosts,
                        hasNextPage: page < totalPages,
                        hasPrevPage: page > 1
                    }
                }
            });

        } catch (error) {
            console.error('❌ Get posts error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to get posts',
                error: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    }

    /**
     * Get posts feed
     */
    static async getFeed(req, res) {
        try {
            const {
                page = 1,
                limit = 20,
                type = 'public',
                userId = null
            } = req.query;

            const skip = (page - 1) * limit;
            let query = {
                isActive: true,
                isDeleted: false,
                moderationStatus: 'approved'
            };

            // Filter by type
            switch (type) {
                case 'public':
                    query.visibility = 'public';
                    break;
                case 'following':
                    if (req.user) {
                        const followingIds = req.user.following || [];
                        query.author = { $in: followingIds };
                        query.visibility = { $in: ['public', 'followers'] };
                    } else {
                        query.visibility = 'public';
                    }
                    break;
                case 'user':
                    if (userId) {
                        query.author = userId;
                        // Check privacy settings
                        if (!req.user || req.user._id.toString() !== userId) {
                            query.visibility = 'public';
                        }
                    }
                    break;
            }

            const posts = await Post.find(query)
                .populate('author', 'username displayName avatar role isVerified')
                .sort({ createdAt: -1 })
                .limit(parseInt(limit))
                .skip(skip)
                .lean();

            // Add user interaction data if authenticated
            if (req.user) {
                posts.forEach(post => {
                    post.isLikedByUser = post.likes.some(like => 
                        like.user.toString() === req.user._id.toString()
                    );
                });
            }

            const totalPosts = await Post.countDocuments(query);
            const hasMore = skip + posts.length < totalPosts;

            res.json({
                success: true,
                data: {
                    posts,
                    pagination: {
                        page: parseInt(page),
                        limit: parseInt(limit),
                        total: totalPosts,
                        hasMore
                    }
                }
            });

        } catch (error) {
            console.error('❌ Get feed error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to get posts feed'
            });
        }
    }

    /**
     * Get single post by ID
     */
    static async getPost(req, res) {
        try {
            const { postId } = req.params;

            const post = await Post.findOne({
                _id: postId,
                isActive: true,
                isDeleted: false
            })
            .populate('author', 'username displayName avatar role isVerified')
            .lean();

            if (!post) {
                return res.status(404).json({
                    success: false,
                    message: 'Post not found'
                });
            }

            // Check if user can view this post
            if (post.visibility === 'private' && 
                (!req.user || req.user._id.toString() !== post.author._id.toString())) {
                return res.status(403).json({
                    success: false,
                    message: 'Access denied'
                });
            }

            if (post.visibility === 'followers' && req.user) {
                const author = await User.findById(post.author._id);
                if (!author.followers.includes(req.user._id) && 
                    req.user._id.toString() !== post.author._id.toString()) {
                    return res.status(403).json({
                        success: false,
                        message: 'Access denied'
                    });
                }
            }

            // Add user interaction data
            if (req.user) {
                post.isLikedByUser = post.likes.some(like => 
                    like.user.toString() === req.user._id.toString()
                );
            }

            // Increment view count
            await Post.findByIdAndUpdate(postId, { $inc: { viewsCount: 1 } });

            res.json({
                success: true,
                data: {
                    post
                }
            });

        } catch (error) {
            console.error('❌ Get post error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to get post'
            });
        }
    }

    /**
     * Update post
     */
    static async updatePost(req, res) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const { postId } = req.params;
            const { content, visibility, allowComments } = req.body;

            const post = await Post.findOne({
                _id: postId,
                isActive: true,
                isDeleted: false
            });

            if (!post) {
                return res.status(404).json({
                    success: false,
                    message: 'Post not found'
                });
            }

            // Check ownership
            if (post.author.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
                return res.status(403).json({
                    success: false,
                    message: 'Access denied'
                });
            }

            // Store original content for edit history
            if (content && content !== post.content) {
                post.editHistory.push({
                    content: post.content,
                    editedAt: Date.now()
                });
                post.isEdited = true;
                post.lastEditedAt = Date.now();
            }

            // Update fields
            if (content) post.content = content;
            if (visibility) post.visibility = visibility;
            if (allowComments !== undefined) post.allowComments = allowComments;

            await post.save();
            await post.populate('author', 'username displayName avatar role isVerified');

            res.json({
                success: true,
                message: 'Post updated successfully',
                data: {
                    post
                }
            });

        } catch (error) {
            console.error('❌ Update post error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to update post'
            });
        }
    }

    /**
     * Delete post
     */
    static async deletePost(req, res) {
        try {
            const { postId } = req.params;

            const post = await Post.findOne({
                _id: postId,
                isActive: true,
                isDeleted: false
            });

            if (!post) {
                return res.status(404).json({
                    success: false,
                    message: 'Post not found'
                });
            }

            // Check ownership or admin
            if (post.author.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
                return res.status(403).json({
                    success: false,
                    message: 'Access denied'
                });
            }

            // Soft delete
            post.isDeleted = true;
            post.isActive = false;
            post.deletedAt = Date.now();
            post.deletedBy = req.user._id;
            await post.save();

            // Update user's post count
            await User.findByIdAndUpdate(post.author, { $inc: { postsCount: -1 } });

            // Clean up images if needed
            if (post.images && post.images.length > 0) {
                const { deleteImage } = require('../middleware/upload');
                post.images.forEach(image => {
                    if (image.public_id) {
                        deleteImage(image.public_id).catch(err => {
                            console.error('Failed to delete image:', err);
                        });
                    }
                });
            }

            res.json({
                success: true,
                message: 'Post deleted successfully'
            });

        } catch (error) {
            console.error('❌ Delete post error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to delete post'
            });
        }
    }

    /**
     * Like/Unlike post
     */
    static async toggleLike(req, res) {
        try {
            const { postId } = req.params;
            const userId = req.user._id;

            const post = await Post.findOne({
                _id: postId,
                isActive: true,
                isDeleted: false
            });

            if (!post) {
                return res.status(404).json({
                    success: false,
                    message: 'Post not found'
                });
            }

            const existingLikeIndex = post.likes.findIndex(like => 
                like.user.toString() === userId.toString()
            );

            let action;
            if (existingLikeIndex > -1) {
                // Unlike
                post.likes.splice(existingLikeIndex, 1);
                action = 'unliked';
            } else {
                // Like
                post.likes.push({ user: userId });
                action = 'liked';

                // Create notification for post author
                if (post.author.toString() !== userId.toString()) {
                    await Notification.createLikeNotification(postId, userId, post.author);
                }
            }

            post.likesCount = post.likes.length;
            await post.save();

            // Emit real-time event
            const io = req.app.get('io');
            if (io) {
                io.to(`post-${postId}`).emit('post-like-updated', {
                    postId,
                    likesCount: post.likesCount,
                    action,
                    userId
                });
            }

            res.json({
                success: true,
                message: `Post ${action} successfully`,
                data: {
                    action,
                    likesCount: post.likesCount,
                    isLiked: action === 'liked'
                }
            });

        } catch (error) {
            console.error('❌ Toggle like error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to toggle like'
            });
        }
    }

    /**
     * Get trending posts
     */
    static async getTrending(req, res) {
        try {
            const { limit = 20 } = req.query;

            const posts = await Post.find({
                visibility: 'public',
                isActive: true,
                isDeleted: false,
                moderationStatus: 'approved',
                createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } // Last 7 days
            })
            .populate('author', 'username displayName avatar role isVerified')
            .sort({ 'engagement.score': -1, likesCount: -1, commentsCount: -1 })
            .limit(parseInt(limit))
            .lean();

            res.json({
                success: true,
                data: {
                    posts
                }
            });

        } catch (error) {
            console.error('❌ Get trending posts error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to get trending posts'
            });
        }
    }

    /**
     * Search posts
     */
    static async searchPosts(req, res) {
        try {
            const { q, page = 1, limit = 20 } = req.query;

            if (!q || q.trim().length < 2) {
                return res.status(400).json({
                    success: false,
                    message: 'Search query must be at least 2 characters'
                });
            }

            const skip = (page - 1) * limit;

            const posts = await Post.find({
                $text: { $search: q },
                visibility: 'public',
                isActive: true,
                isDeleted: false,
                moderationStatus: 'approved'
            })
            .populate('author', 'username displayName avatar role isVerified')
            .sort({ score: { $meta: 'textScore' }, createdAt: -1 })
            .limit(parseInt(limit))
            .skip(skip)
            .lean();

            const totalResults = await Post.countDocuments({
                $text: { $search: q },
                visibility: 'public',
                isActive: true,
                isDeleted: false,
                moderationStatus: 'approved'
            });

            res.json({
                success: true,
                data: {
                    posts,
                    pagination: {
                        page: parseInt(page),
                        limit: parseInt(limit),
                        total: totalResults,
                        hasMore: skip + posts.length < totalResults
                    },
                    query: q
                }
            });

        } catch (error) {
            console.error('❌ Search posts error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to search posts'
            });
        }
    }

    /**
     * Report post
     */
    static async reportPost(req, res) {
        try {
            const { postId } = req.params;
            const { reason, description } = req.body;

            const post = await Post.findById(postId);

            if (!post) {
                return res.status(404).json({
                    success: false,
                    message: 'Post not found'
                });
            }

            // Check if user already reported this post
            const existingReport = post.reports.find(report => 
                report.reporter.toString() === req.user._id.toString()
            );

            if (existingReport) {
                return res.status(400).json({
                    success: false,
                    message: 'You have already reported this post'
                });
            }

            // Add report
            post.reports.push({
                reporter: req.user._id,
                reason,
                description
            });

            post.reportCount = post.reports.length;
            post.isReported = true;

            // Auto-flag if too many reports
            if (post.reportCount >= 5) {
                post.moderationStatus = 'flagged';
            }

            await post.save();

            res.json({
                success: true,
                message: 'Post reported successfully'
            });

        } catch (error) {
            console.error('❌ Report post error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to report post'
            });
        }
    }
}

module.exports = PostController;
