{"name": "barber-brothers-community-backend", "version": "1.0.0", "description": "Backend API for Barber Brothers Community Hub", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "keywords": ["barber", "community", "social", "api", "nodejs", "express"], "author": "<PERSON> - Barber Brothers Legacy", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "cloudinary": "^1.41.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^7.6.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "multer-storage-cloudinary": "^4.0.0", "nodemailer": "^6.9.7", "passport": "^0.6.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "socket.io": "^4.7.4"}, "devDependencies": {"eslint": "^8.53.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/andreb17/dre1z78.git"}, "bugs": {"url": "https://github.com/andreb17/dre1z78/issues"}, "homepage": "https://www.barberbrotherz.com"}