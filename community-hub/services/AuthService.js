/**
 * Authentication Service for Barber Brothers Community Hub
 * Integrates with existing Google OAuth system and manages user sessions
 */

class AuthService {
    constructor() {
        this.currentUser = null;
        this.authStateListeners = [];
        this.isInitialized = false;
        
        // Initialize Firebase if not already done
        this.initializeFirebase();
        
        // Listen for auth state changes
        this.setupAuthStateListener();
        
        // Check for existing authentication from main site
        this.checkExistingAuth();
    }

    /**
     * Initialize Firebase Authentication
     * Uses centralized Firebase configuration
     */
    async initializeFirebase() {
        try {
            // Check if Firebase is already initialized globally
            if (window.firebaseApp && window.firebaseAuth && window.firebaseDb) {
                console.log('✅ AuthService: Using existing Firebase initialization');
                this.auth = window.firebaseAuth;
                this.db = window.firebaseDb;
                this.isInitialized = true;
                return;
            }

            // Firebase config - matches centralized configuration
            const firebaseConfig = {
                apiKey: "AIzaSyAHqpO8PPXmZIfype7dsViz3chKcmLdmpY",
                authDomain: "barber-brothers-legacy.firebaseapp.com",
                projectId: "barber-brothers-legacy",
                storageBucket: "barber-brothers-legacy.appspot.com",
                messagingSenderId: "946338896038",
                appId: "1:946338896038:web:d65f5bef7973127d1abc67"
            };

            // Initialize Firebase if not already initialized
            if (!firebase.apps.length) {
                firebase.initializeApp(firebaseConfig);
                console.log('✅ AuthService: Firebase initialized');
            } else {
                console.log('✅ AuthService: Firebase already initialized');
            }

            this.auth = firebase.auth();
            this.db = firebase.firestore();
            this.isInitialized = true;

            // Store references globally for consistency
            window.firebaseApp = firebase.app();
            window.firebaseAuth = this.auth;
            window.firebaseDb = this.db;
            window.firebaseStorage = firebase.storage();

            console.log('✅ AuthService: Firebase services initialized successfully');
        } catch (error) {
            console.error('❌ AuthService: Firebase initialization failed:', error);
            throw new Error('Failed to initialize authentication service');
        }
    }

    /**
     * Setup authentication state listener
     */
    setupAuthStateListener() {
        if (!this.auth) return;

        this.auth.onAuthStateChanged(async (user) => {
            console.log('🔐 AuthService: Auth state changed:', user ? user.email : 'No user');
            
            if (user) {
                // User is signed in
                await this.handleUserSignIn(user);
            } else {
                // User is signed out
                this.handleUserSignOut();
            }

            // Notify all listeners
            this.notifyAuthStateListeners(this.currentUser);
        });
    }

    /**
     * Handle user sign in - create/update user profile
     */
    async handleUserSignIn(firebaseUser) {
        try {
            // Create user profile object
            const userProfile = {
                uid: firebaseUser.uid,
                email: firebaseUser.email,
                displayName: firebaseUser.displayName || 'Barber Brothers User',
                photoURL: firebaseUser.photoURL || '/images/time.jpeg',
                emailVerified: firebaseUser.emailVerified,
                lastLoginAt: firebase.firestore.FieldValue.serverTimestamp(),
                isOnline: true
            };

            // Check if user exists in Firestore
            const userDoc = await this.db.collection('users').doc(firebaseUser.uid).get();
            
            if (!userDoc.exists) {
                // New user - create profile with defaults
                const newUserData = {
                    ...userProfile,
                    bio: '',
                    location: '',
                    joinDate: firebase.firestore.FieldValue.serverTimestamp(),
                    followers: [],
                    following: [],
                    postsCount: 0,
                    isVerified: false,
                    role: 'user',
                    preferences: {
                        notifications: true,
                        privacy: 'public',
                        emailUpdates: true
                    }
                };

                await this.db.collection('users').doc(firebaseUser.uid).set(newUserData);
                this.currentUser = { ...newUserData, id: firebaseUser.uid };
                
                console.log('✅ AuthService: New user profile created');
            } else {
                // Existing user - update login info
                await this.db.collection('users').doc(firebaseUser.uid).update({
                    lastLoginAt: firebase.firestore.FieldValue.serverTimestamp(),
                    isOnline: true,
                    // Update profile info if changed
                    displayName: userProfile.displayName,
                    photoURL: userProfile.photoURL,
                    emailVerified: userProfile.emailVerified
                });

                const userData = userDoc.data();
                this.currentUser = { ...userData, id: firebaseUser.uid };
                
                console.log('✅ AuthService: Existing user signed in');
            }

            // Store auth data in localStorage for main site integration
            localStorage.setItem('communityAuthUser', JSON.stringify(this.currentUser));
            localStorage.setItem('authSuccess', 'true');

        } catch (error) {
            console.error('❌ AuthService: Error handling user sign in:', error);
            throw error;
        }
    }

    /**
     * Handle user sign out
     */
    handleUserSignOut() {
        if (this.currentUser) {
            // Update user's online status
            this.db.collection('users').doc(this.currentUser.id).update({
                isOnline: false,
                lastSeenAt: firebase.firestore.FieldValue.serverTimestamp()
            }).catch(error => {
                console.warn('⚠️ AuthService: Could not update offline status:', error);
            });
        }

        this.currentUser = null;
        
        // Clear localStorage
        localStorage.removeItem('communityAuthUser');
        localStorage.removeItem('authSuccess');
        localStorage.removeItem('userProfile');

        console.log('✅ AuthService: User signed out');
    }

    /**
     * Check for existing authentication from main site
     */
    checkExistingAuth() {
        try {
            // Check if user is already authenticated from main site
            const authSuccess = localStorage.getItem('authSuccess');
            const userProfile = localStorage.getItem('userProfile');
            const googleAuthData = localStorage.getItem('googleAuthData');

            if (authSuccess === 'true' && userProfile) {
                console.log('🔍 AuthService: Found existing auth from main site');
                
                // Parse user data
                const userData = JSON.parse(userProfile);
                
                // Sign in with existing Google auth if available
                if (googleAuthData) {
                    const authData = JSON.parse(googleAuthData);
                    this.signInWithExistingAuth(userData, authData);
                }
            }
        } catch (error) {
            console.warn('⚠️ AuthService: Error checking existing auth:', error);
        }
    }

    /**
     * Sign in with existing authentication data
     */
    async signInWithExistingAuth(userData, authData) {
        try {
            // This would typically involve validating the auth token
            // For now, we'll create a session based on the existing data
            console.log('🔄 AuthService: Signing in with existing auth data');
            
            // You might want to validate the token with your backend here
            // For now, we'll trust the existing authentication
            
        } catch (error) {
            console.error('❌ AuthService: Error with existing auth:', error);
        }
    }

    /**
     * Sign in with Google (redirect to main site OAuth)
     */
    async signInWithGoogle() {
        try {
            // Use the existing Google OAuth from the main site
            if (typeof window.redirectToGoogleSignIn === 'function') {
                // Store that we came from community hub
                localStorage.setItem('authRedirectSource', 'community-hub');
                window.redirectToGoogleSignIn('community');
            } else {
                // Fallback to Firebase Google sign-in
                const provider = new firebase.auth.GoogleAuthProvider();
                provider.addScope('email');
                provider.addScope('profile');
                
                const result = await this.auth.signInWithPopup(provider);
                console.log('✅ AuthService: Google sign-in successful');
                return result.user;
            }
        } catch (error) {
            console.error('❌ AuthService: Google sign-in failed:', error);
            throw error;
        }
    }

    /**
     * Sign out user
     */
    async signOut() {
        try {
            await this.auth.signOut();
            console.log('✅ AuthService: User signed out successfully');
        } catch (error) {
            console.error('❌ AuthService: Sign out failed:', error);
            throw error;
        }
    }

    /**
     * Get current user
     */
    getCurrentUser() {
        return this.currentUser;
    }

    /**
     * Check if user is authenticated
     */
    isAuthenticated() {
        return this.currentUser !== null;
    }

    /**
     * Check if user has specific role
     */
    hasRole(role) {
        return this.currentUser && this.currentUser.role === role;
    }

    /**
     * Add auth state change listener
     */
    onAuthStateChanged(callback) {
        this.authStateListeners.push(callback);
        
        // Immediately call with current state
        callback(this.currentUser);
        
        // Return unsubscribe function
        return () => {
            const index = this.authStateListeners.indexOf(callback);
            if (index > -1) {
                this.authStateListeners.splice(index, 1);
            }
        };
    }

    /**
     * Notify all auth state listeners
     */
    notifyAuthStateListeners(user) {
        this.authStateListeners.forEach(callback => {
            try {
                callback(user);
            } catch (error) {
                console.error('❌ AuthService: Error in auth state listener:', error);
            }
        });
    }

    /**
     * Update user profile
     */
    async updateUserProfile(updates) {
        if (!this.currentUser) {
            throw new Error('No authenticated user');
        }

        try {
            await this.db.collection('users').doc(this.currentUser.id).update({
                ...updates,
                updatedAt: firebase.firestore.FieldValue.serverTimestamp()
            });

            // Update local user object
            this.currentUser = { ...this.currentUser, ...updates };
            
            // Update localStorage
            localStorage.setItem('communityAuthUser', JSON.stringify(this.currentUser));
            
            console.log('✅ AuthService: User profile updated');
            
            // Notify listeners
            this.notifyAuthStateListeners(this.currentUser);
            
        } catch (error) {
            console.error('❌ AuthService: Failed to update user profile:', error);
            throw error;
        }
    }

    /**
     * Get user by ID
     */
    async getUserById(userId) {
        try {
            const userDoc = await this.db.collection('users').doc(userId).get();
            if (userDoc.exists) {
                return { id: userId, ...userDoc.data() };
            }
            return null;
        } catch (error) {
            console.error('❌ AuthService: Error getting user by ID:', error);
            throw error;
        }
    }
}

// Export singleton instance
export default new AuthService();
