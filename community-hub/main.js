/**
 * Barber Brothers Community Hub - Main Entry Point
 * Initializes and manages the community hub features
 */

import AuthManager from './components/auth/AuthManager.js';

class CommunityHub {
    constructor() {
        this.authManager = null;
        this.socialFeed = null;
        this.currentView = 'feed';
        this.isInitialized = false;
        
        this.init();
    }

    /**
     * Initialize the community hub
     */
    async init() {
        try {
            console.log('🏛️ CommunityHub: Initializing...');
            
            // Wait for DOM to be ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.init());
                return;
            }
            
            // Load CSS styles
            await this.loadStyles();
            
            // Initialize authentication manager
            this.authManager = AuthManager;
            
            // Setup navigation
            this.setupNavigation();
            
            // Initialize default view
            await this.initializeDefaultView();
            
            // Setup global event listeners
            this.setupGlobalEventListeners();
            
            this.isInitialized = true;
            console.log('✅ CommunityHub: Initialized successfully');
            
            // Dispatch ready event
            window.dispatchEvent(new CustomEvent('communityHubReady'));
            
        } catch (error) {
            console.error('❌ CommunityHub: Initialization failed:', error);
        }
    }

    /**
     * Load CSS styles
     */
    async loadStyles() {
        return new Promise((resolve, reject) => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = '/community-hub/styles/community-hub.css';
            link.onload = resolve;
            link.onerror = reject;
            document.head.appendChild(link);
        });
    }

    /**
     * Setup navigation for community hub
     */
    setupNavigation() {
        // Check if community section exists in the main HTML
        const communitySection = document.getElementById('community');
        if (!communitySection) {
            console.warn('Community section not found in HTML');
            return;
        }

        // Setup community hub container within the existing section
        const communityRoot = document.getElementById('community-feed-root');
        if (communityRoot && !communityRoot.querySelector('.community-hub-content')) {
            // Hide loading spinner and features initially
            const loadingSpinner = communityRoot.querySelector('.spinner-border')?.parentElement;
            const featuresSection = document.getElementById('community-features');

            if (loadingSpinner) {
                loadingSpinner.style.display = 'none';
            }
            if (featuresSection) {
                featuresSection.style.display = 'none';
            }

            // Create a wrapper that will contain both navigation and the React component
            communityRoot.innerHTML = `
                <div class="community-hub-content">
                    <div class="community-navigation mb-4">
                        <div class="btn-group w-100" role="group">
                            <button class="btn btn-outline-danger active" data-view="feed">
                                <i class="fas fa-home me-2"></i>Feed
                            </button>
                            <button class="btn btn-outline-danger" data-view="profile">
                                <i class="fas fa-user me-2"></i>Profile
                            </button>
                            <button class="btn btn-outline-danger" data-view="discover">
                                <i class="fas fa-compass me-2"></i>Discover
                            </button>
                        </div>
                    </div>

                    <div class="community-content">
                        <div id="community-feed" class="community-view active">
                            <!-- React component will mount here -->
                            <div id="react-feed-mount"></div>
                        </div>
                        <div id="community-profile" class="community-view" style="display: none;"></div>
                        <div id="community-discover" class="community-view" style="display: none;"></div>
                    </div>
                </div>
            `;
        }
    }

    /**
     * Initialize the default view (feed)
     */
    async initializeDefaultView() {
        await this.showView('feed');
    }

    /**
     * Show a specific view
     */
    async showView(viewName) {
        try {
            console.log(`🔄 CommunityHub: Switching to ${viewName} view`);
            
            // Hide all views
            document.querySelectorAll('.community-view').forEach(view => {
                view.classList.remove('active');
                view.style.display = 'none';
            });
            
            // Update navigation
            document.querySelectorAll('.nav-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            const activeBtn = document.querySelector(`[data-view="${viewName}"]`);
            if (activeBtn) {
                activeBtn.classList.add('active');
            }
            
            // Show selected view
            const viewContainer = document.getElementById(`community-${viewName}`);
            if (viewContainer) {
                viewContainer.classList.add('active');
                viewContainer.style.display = 'block';
            }
            
            // Initialize view content
            switch (viewName) {
                case 'feed':
                    await this.initializeFeedView();
                    break;
                case 'profile':
                    await this.initializeProfileView();
                    break;
                case 'discover':
                    await this.initializeDiscoverView();
                    break;
            }
            
            this.currentView = viewName;
            
        } catch (error) {
            console.error(`❌ CommunityHub: Failed to show ${viewName} view:`, error);
        }
    }

    /**
     * Initialize feed view
     */
    async initializeFeedView() {
        const feedContainer = document.getElementById('community-feed');
        if (feedContainer) {
            console.log('🏛️ CommunityHub: Initializing feed view');

            // Check if the community feed is already loaded
            if (!feedContainer.querySelector('.community-feed-container')) {
                // Add a loading placeholder
                feedContainer.innerHTML = `
                    <div class="text-center py-4">
                        <div class="spinner-border text-danger" role="status">
                            <span class="visually-hidden">Loading community feed...</span>
                        </div>
                        <p class="mt-2 text-white">Loading community feed...</p>
                    </div>
                `;

                // The community-feed.js script will replace this content
                console.log('🏛️ CommunityHub: Feed placeholder added, waiting for community-feed.js');
            }
        }
    }

    /**
     * Initialize profile view
     */
    async initializeProfileView() {
        const profileContainer = document.getElementById('community-profile');
        if (profileContainer && !profileContainer.hasChildNodes()) {
            console.log('🏛️ CommunityHub: Initializing profile view');

            profileContainer.innerHTML = `
                <div class="profile-content">
                    <div class="profile-header text-center mb-4">
                        <img src="images/time.jpeg" alt="User Profile" class="rounded-circle mb-3" width="100" height="100">
                        <h3 class="text-white">Your Profile</h3>
                        <p class="text-muted">Manage your community presence</p>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="profile-card p-3 bg-black bg-opacity-50 rounded border border-danger border-opacity-25">
                                <h5 class="text-white mb-3"><i class="fas fa-user me-2"></i>Profile Info</h5>
                                <div class="mb-3">
                                    <label class="form-label text-white">Display Name</label>
                                    <input type="text" class="form-control bg-dark text-white border-danger" placeholder="Enter your name">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label text-white">Bio</label>
                                    <textarea class="form-control bg-dark text-white border-danger" rows="3" placeholder="Tell us about yourself"></textarea>
                                </div>
                                <button class="btn btn-danger">Update Profile</button>
                            </div>
                        </div>

                        <div class="col-md-6 mb-4">
                            <div class="profile-card p-3 bg-black bg-opacity-50 rounded border border-danger border-opacity-25">
                                <h5 class="text-white mb-3"><i class="fas fa-chart-bar me-2"></i>Your Stats</h5>
                                <div class="stat-item d-flex justify-content-between mb-2">
                                    <span class="text-muted">Posts</span>
                                    <span class="text-white">12</span>
                                </div>
                                <div class="stat-item d-flex justify-content-between mb-2">
                                    <span class="text-muted">Likes Received</span>
                                    <span class="text-white">156</span>
                                </div>
                                <div class="stat-item d-flex justify-content-between mb-2">
                                    <span class="text-muted">Comments</span>
                                    <span class="text-white">43</span>
                                </div>
                                <div class="stat-item d-flex justify-content-between">
                                    <span class="text-muted">Member Since</span>
                                    <span class="text-white">Jan 2025</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="profile-posts">
                        <h5 class="text-white mb-3"><i class="fas fa-images me-2"></i>Your Recent Posts</h5>
                        <div class="text-center py-4">
                            <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No posts yet. Share your first post in the Feed!</p>
                            <button class="btn btn-outline-danger" onclick="window.CommunityHub.showView('feed')">
                                Create Your First Post
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    /**
     * Initialize discover view
     */
    async initializeDiscoverView() {
        const discoverContainer = document.getElementById('community-discover');
        if (discoverContainer && !discoverContainer.hasChildNodes()) {
            console.log('🏛️ CommunityHub: Initializing discover view');

            discoverContainer.innerHTML = `
                <div class="discover-content">
                    <div class="discover-header text-center mb-4">
                        <h3 class="text-white mb-3">
                            <i class="fas fa-compass me-2"></i>Discover
                        </h3>
                        <p class="text-muted">Find trending content and connect with the community</p>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="discover-card p-3 bg-black bg-opacity-50 rounded border border-danger border-opacity-25">
                                <h5 class="text-white mb-3"><i class="fas fa-fire me-2"></i>Trending Topics</h5>
                                <div class="trending-item d-flex justify-content-between align-items-center mb-2 p-2 rounded bg-dark">
                                    <span class="text-white">#FreshFades</span>
                                    <span class="badge bg-danger">245 posts</span>
                                </div>
                                <div class="trending-item d-flex justify-content-between align-items-center mb-2 p-2 rounded bg-dark">
                                    <span class="text-white">#BarberLife</span>
                                    <span class="badge bg-danger">189 posts</span>
                                </div>
                                <div class="trending-item d-flex justify-content-between align-items-center mb-2 p-2 rounded bg-dark">
                                    <span class="text-white">#BeardTrim</span>
                                    <span class="badge bg-danger">156 posts</span>
                                </div>
                                <div class="trending-item d-flex justify-content-between align-items-center p-2 rounded bg-dark">
                                    <span class="text-white">#StyleTips</span>
                                    <span class="badge bg-danger">134 posts</span>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 mb-4">
                            <div class="discover-card p-3 bg-black bg-opacity-50 rounded border border-danger border-opacity-25">
                                <h5 class="text-white mb-3"><i class="fas fa-users me-2"></i>Featured Barbers</h5>
                                <div class="featured-barber d-flex align-items-center mb-3 p-2 rounded bg-dark">
                                    <img src="images/time.jpeg" alt="Andre" class="rounded-circle me-3" width="40" height="40">
                                    <div>
                                        <h6 class="text-white mb-0">Andre The Barber</h6>
                                        <small class="text-muted">Master Barber • 523 posts</small>
                                    </div>
                                </div>
                                <div class="featured-barber d-flex align-items-center mb-3 p-2 rounded bg-dark">
                                    <img src="images/time.jpeg" alt="Community" class="rounded-circle me-3" width="40" height="40">
                                    <div>
                                        <h6 class="text-white mb-0">Barber Brothers Media</h6>
                                        <small class="text-muted">Official Account • 156 posts</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="discover-actions text-center">
                        <h5 class="text-white mb-3"><i class="fas fa-search me-2"></i>Explore More</h5>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <button class="btn btn-outline-danger w-100">
                                    <i class="fas fa-hashtag me-2"></i>Browse Tags
                                </button>
                            </div>
                            <div class="col-md-4 mb-3">
                                <button class="btn btn-outline-danger w-100">
                                    <i class="fas fa-user-friends me-2"></i>Find Barbers
                                </button>
                            </div>
                            <div class="col-md-4 mb-3">
                                <button class="btn btn-outline-danger w-100">
                                    <i class="fas fa-star me-2"></i>Top Posts
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    /**
     * Setup global event listeners
     */
    setupGlobalEventListeners() {
        // Navigation clicks
        document.addEventListener('click', (e) => {
            // Community navigation
            if (e.target.matches('[data-view]') || e.target.closest('[data-view]')) {
                e.preventDefault();
                const button = e.target.matches('[data-view]') ? e.target : e.target.closest('[data-view]');
                const view = button.dataset.view;
                this.showView(view);
            }
        });

        // Intersection Observer for community section visibility
        const communitySection = document.getElementById('community');
        if (communitySection) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        // Community section is visible, initialize if needed
                        if (!this.isInitialized) {
                            this.initializeDefaultView();
                        }
                    }
                });
            }, { threshold: 0.1 });

            observer.observe(communitySection);
        }
    }

    /**
     * Scroll to community section
     */
    scrollToCommunity() {
        const communitySection = document.getElementById('community');
        if (communitySection) {
            communitySection.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    }

    /**
     * Get current user
     */
    getCurrentUser() {
        return this.authManager ? this.authManager.getCurrentUser() : null;
    }

    /**
     * Check if user is authenticated
     */
    isAuthenticated() {
        return this.authManager ? this.authManager.isAuthenticated() : false;
    }

    /**
     * Cleanup
     */
    destroy() {
        if (this.socialFeed) {
            this.socialFeed.destroy();
        }
        
        if (this.authManager) {
            this.authManager.destroy();
        }
    }
}

// Initialize community hub when script loads
const communityHub = new CommunityHub();

// Export for global access
window.CommunityHub = communityHub;

export default CommunityHub;
