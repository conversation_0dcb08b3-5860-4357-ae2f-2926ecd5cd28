<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google OAuth Debug Tool - Barber Brothers</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background: #1a1a1a; color: #fff; font-family: 'Roboto', sans-serif; }
        .debug-container { max-width: 800px; margin: 50px auto; padding: 20px; }
        .debug-card { background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); border-radius: 15px; padding: 20px; margin-bottom: 20px; }
        .code-block { background: #000; color: #00ff00; padding: 15px; border-radius: 8px; font-family: 'Courier New', monospace; font-size: 12px; }
        .btn-debug { background: linear-gradient(45deg, #dc3545, #ff6b6b); border: none; color: white; }
        .status-good { color: #28a745; }
        .status-bad { color: #dc3545; }
        .status-warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1 class="text-center mb-4">🔧 Google OAuth Debug Tool</h1>
        <p class="text-center lead">Diagnose and fix your Google OAuth redirect_uri_mismatch error</p>

        <!-- Current Configuration -->
        <div class="debug-card">
            <h3>📋 Current Configuration</h3>
            <div id="current-config">
                <p><strong>Current URL:</strong> <span id="current-url"></span></p>
                <p><strong>Origin:</strong> <span id="current-origin"></span></p>
                <p><strong>Hostname:</strong> <span id="current-hostname"></span></p>
                <p><strong>Protocol:</strong> <span id="current-protocol"></span></p>
                <p><strong>Port:</strong> <span id="current-port"></span></p>
            </div>
        </div>

        <!-- OAuth Configuration -->
        <div class="debug-card">
            <h3>🔑 OAuth Configuration</h3>
            <div id="oauth-config">
                <p><strong>Client ID:</strong> <span id="client-id"></span></p>
                <p><strong>Redirect URI (Generated):</strong> <span id="redirect-uri" class="status-warning"></span></p>
                <p><strong>Scopes:</strong> <span id="scopes"></span></p>
            </div>
        </div>

        <!-- Google Cloud Console Setup -->
        <div class="debug-card">
            <h3>☁️ Google Cloud Console Setup Required</h3>
            <p>You need to add this redirect URI to your Google Cloud Console:</p>
            <div class="code-block" id="required-redirect-uri">
                Loading...
            </div>
            <div class="mt-3">
                <h5>Steps to fix:</h5>
                <ol>
                    <li>Go to <a href="https://console.cloud.google.com/apis/credentials" target="_blank" class="text-info">Google Cloud Console - Credentials</a></li>
                    <li>Find your OAuth 2.0 Client ID: <code id="client-id-copy"></code></li>
                    <li>Click on it to edit</li>
                    <li>In "Authorized redirect URIs", add: <strong id="uri-to-add" class="status-good"></strong></li>
                    <li>Click "Save"</li>
                    <li>Wait 5-10 minutes for changes to propagate</li>
                    <li>Try signing in again</li>
                </ol>
            </div>
        </div>

        <!-- Test OAuth URL -->
        <div class="debug-card">
            <h3>🧪 Test OAuth URL</h3>
            <button class="btn btn-debug" onclick="generateTestURL()">Generate Test OAuth URL</button>
            <div id="test-url-result" class="mt-3" style="display: none;">
                <p><strong>Generated OAuth URL:</strong></p>
                <div class="code-block" id="test-oauth-url"></div>
                <button class="btn btn-outline-light btn-sm mt-2" onclick="copyToClipboard('test-oauth-url')">Copy URL</button>
                <button class="btn btn-outline-success btn-sm mt-2" onclick="testOAuthURL()">Test This URL</button>
            </div>
        </div>

        <!-- Quick Fix -->
        <div class="debug-card">
            <h3>⚡ Quick Fix for Local Development</h3>
            <p>If you're testing locally, make sure these URIs are in your Google Cloud Console:</p>
            <div class="code-block">
http://localhost:3000/auth-callback.html
http://127.0.0.1:3000/auth-callback.html
https://www.barberbrotherz.com/auth-callback.html
            </div>
        </div>

        <!-- Status Check -->
        <div class="debug-card">
            <h3>✅ Status Check</h3>
            <div id="status-check">
                <p id="config-status">⏳ Checking configuration...</p>
                <p id="url-status">⏳ Checking URL generation...</p>
                <p id="env-status">⏳ Checking environment...</p>
            </div>
        </div>
    </div>

    <!-- Include the Google Auth Config -->
    <script src="google-auth-config.js"></script>
    
    <script>
        function updateCurrentInfo() {
            document.getElementById('current-url').textContent = window.location.href;
            document.getElementById('current-origin').textContent = window.location.origin;
            document.getElementById('current-hostname').textContent = window.location.hostname;
            document.getElementById('current-protocol').textContent = window.location.protocol;
            document.getElementById('current-port').textContent = window.location.port || 'default';
        }

        function updateOAuthConfig() {
            if (typeof GOOGLE_AUTH_CONFIG !== 'undefined') {
                document.getElementById('client-id').textContent = GOOGLE_AUTH_CONFIG.clientId;
                document.getElementById('client-id-copy').textContent = GOOGLE_AUTH_CONFIG.clientId;
                
                const redirectUri = GOOGLE_AUTH_CONFIG.redirectUri;
                document.getElementById('redirect-uri').textContent = redirectUri;
                document.getElementById('required-redirect-uri').textContent = redirectUri;
                document.getElementById('uri-to-add').textContent = redirectUri;
                
                document.getElementById('scopes').textContent = GOOGLE_AUTH_CONFIG.scopes.join(', ');
                
                // Status updates
                document.getElementById('config-status').innerHTML = '✅ <span class="status-good">OAuth configuration loaded</span>';
            } else {
                document.getElementById('config-status').innerHTML = '❌ <span class="status-bad">OAuth configuration not found</span>';
            }
        }

        function generateTestURL() {
            try {
                if (typeof getGoogleAuthUrl === 'function') {
                    const testUrl = getGoogleAuthUrl('debug-test');
                    document.getElementById('test-oauth-url').textContent = testUrl;
                    document.getElementById('test-url-result').style.display = 'block';
                    document.getElementById('url-status').innerHTML = '✅ <span class="status-good">URL generation working</span>';
                } else {
                    throw new Error('getGoogleAuthUrl function not available');
                }
            } catch (error) {
                document.getElementById('url-status').innerHTML = `❌ <span class="status-bad">URL generation failed: ${error.message}</span>';
                console.error('URL generation error:', error);
            }
        }

        function testOAuthURL() {
            const url = document.getElementById('test-oauth-url').textContent;
            if (url) {
                console.log('🧪 Testing OAuth URL:', url);
                window.open(url, '_blank');
            }
        }

        function copyToClipboard(elementId) {
            const text = document.getElementById(elementId).textContent;
            navigator.clipboard.writeText(text).then(() => {
                alert('Copied to clipboard!');
            }).catch(err => {
                console.error('Failed to copy:', err);
            });
        }

        function checkEnvironment() {
            const isHTTPS = window.location.protocol === 'https:';
            const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
            const isProduction = window.location.hostname.includes('barberbrotherz.com');

            let envStatus = '';
            if (isProduction) {
                envStatus = '🌐 <span class="status-good">Production environment detected</span>';
            } else if (isLocalhost) {
                envStatus = '🏠 <span class="status-warning">Local development environment</span>';
            } else {
                envStatus = '🔧 <span class="status-warning">Development environment</span>';
            }

            if (!isHTTPS && !isLocalhost) {
                envStatus += '<br>⚠️ <span class="status-bad">HTTPS required for OAuth in production</span>';
            }

            document.getElementById('env-status').innerHTML = envStatus;
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateCurrentInfo();
            updateOAuthConfig();
            checkEnvironment();
            
            // Auto-generate test URL
            setTimeout(generateTestURL, 500);
        });

        // Add some helpful console logs
        console.log('🔧 OAuth Debug Tool Loaded');
        console.log('📍 Current location:', window.location.href);
        console.log('🔑 OAuth Config:', typeof GOOGLE_AUTH_CONFIG !== 'undefined' ? GOOGLE_AUTH_CONFIG : 'Not loaded');
    </script>
</body>
</html>
