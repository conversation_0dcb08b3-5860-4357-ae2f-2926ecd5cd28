<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firebase Configuration Test - Barber Brothers Legacy</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        .test-section {
            background-color: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #dc2626;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #065f46;
            border: 1px solid #10b981;
        }
        .error {
            background-color: #7f1d1d;
            border: 1px solid #ef4444;
        }
        .warning {
            background-color: #78350f;
            border: 1px solid #f59e0b;
        }
        button {
            background-color: #dc2626;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #b91c1c;
        }
        .config-display {
            background-color: #374151;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>🔥 Firebase Configuration Test</h1>
    <p>This page tests the Firebase configuration for Barber Brothers Legacy.</p>

    <div class="test-section">
        <h2>📋 Configuration Status</h2>
        <div id="config-status"></div>
        <div id="config-display" class="config-display"></div>
    </div>

    <div class="test-section">
        <h2>🔐 Authentication Test</h2>
        <div id="auth-status"></div>
        <button onclick="testGoogleSignIn()">Test Google Sign-In</button>
        <button onclick="testSignOut()">Sign Out</button>
        <div id="user-info"></div>
    </div>

    <div class="test-section">
        <h2>📊 Firestore Test</h2>
        <div id="firestore-status"></div>
        <button onclick="testFirestoreWrite()">Test Write</button>
        <button onclick="testFirestoreRead()">Test Read</button>
        <div id="firestore-results"></div>
    </div>

    <div class="test-section">
        <h2>📁 Storage Test</h2>
        <div id="storage-status"></div>
        <input type="file" id="file-input" accept="image/*">
        <button onclick="testStorageUpload()">Test Upload</button>
        <div id="storage-results"></div>
    </div>

    <div class="test-section">
        <h2>📈 Analytics Test</h2>
        <div id="analytics-status"></div>
        <button onclick="testAnalytics()">Test Analytics Event</button>
    </div>

    <!-- Firebase v10 Integration -->
    <script src="https://www.gstatic.com/firebasejs/10.14.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.14.1/firebase-firestore-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.14.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.14.1/firebase-storage-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.14.1/firebase-analytics-compat.js"></script>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyAHqpO8PPXmZIfype7dsViz3chKcmLdmpY",
            authDomain: "barber-brothers-legacy.firebaseapp.com",
            projectId: "barber-brothers-legacy",
            storageBucket: "barber-brothers-legacy.appspot.com",
            messagingSenderId: "946338896038",
            appId: "1:946338896038:web:d65f5bef7973127d1abc67",
            measurementId: "G-XXXXXXXXXX"
        };

        let app, auth, db, storage, analytics;

        // Initialize Firebase
        function initializeFirebase() {
            try {
                app = firebase.initializeApp(firebaseConfig);
                auth = firebase.auth();
                db = firebase.firestore();
                storage = firebase.storage();
                
                // Try to initialize analytics
                try {
                    analytics = firebase.analytics();
                } catch (analyticsError) {
                    console.warn('Analytics not available:', analyticsError);
                }

                displayResult('config-status', 'Firebase initialized successfully!', 'success');
                displayConfig();
                setupAuthListener();
                
                return true;
            } catch (error) {
                displayResult('config-status', `Firebase initialization failed: ${error.message}`, 'error');
                return false;
            }
        }

        // Display configuration
        function displayConfig() {
            const configDisplay = document.getElementById('config-display');
            const safeConfig = {
                ...firebaseConfig,
                apiKey: firebaseConfig.apiKey.substring(0, 10) + '...',
            };
            configDisplay.textContent = JSON.stringify(safeConfig, null, 2);
        }

        // Setup authentication listener
        function setupAuthListener() {
            auth.onAuthStateChanged((user) => {
                if (user) {
                    displayResult('auth-status', `Signed in as: ${user.email}`, 'success');
                    document.getElementById('user-info').innerHTML = `
                        <div class="success">
                            <strong>User Info:</strong><br>
                            Email: ${user.email}<br>
                            Display Name: ${user.displayName || 'Not set'}<br>
                            UID: ${user.uid}
                        </div>
                    `;
                } else {
                    displayResult('auth-status', 'Not signed in', 'warning');
                    document.getElementById('user-info').innerHTML = '';
                }
            });
        }

        // Test Google Sign-In
        async function testGoogleSignIn() {
            try {
                const provider = new firebase.auth.GoogleAuthProvider();
                const result = await auth.signInWithPopup(provider);
                displayResult('auth-status', `Google sign-in successful: ${result.user.email}`, 'success');
            } catch (error) {
                displayResult('auth-status', `Google sign-in failed: ${error.message}`, 'error');
            }
        }

        // Test Sign Out
        async function testSignOut() {
            try {
                await auth.signOut();
                displayResult('auth-status', 'Signed out successfully', 'success');
            } catch (error) {
                displayResult('auth-status', `Sign out failed: ${error.message}`, 'error');
            }
        }

        // Test Firestore Write
        async function testFirestoreWrite() {
            try {
                const testDoc = {
                    message: 'Firebase test message',
                    timestamp: firebase.firestore.FieldValue.serverTimestamp(),
                    testId: Date.now()
                };
                
                const docRef = await db.collection('test').add(testDoc);
                displayResult('firestore-status', `Firestore write successful: ${docRef.id}`, 'success');
                
                // Store the doc ID for reading
                window.lastTestDocId = docRef.id;
            } catch (error) {
                displayResult('firestore-status', `Firestore write failed: ${error.message}`, 'error');
            }
        }

        // Test Firestore Read
        async function testFirestoreRead() {
            try {
                const snapshot = await db.collection('test').limit(5).get();
                const docs = [];
                snapshot.forEach(doc => {
                    docs.push({ id: doc.id, ...doc.data() });
                });
                
                displayResult('firestore-status', `Firestore read successful: ${docs.length} documents`, 'success');
                document.getElementById('firestore-results').innerHTML = `
                    <div class="success">
                        <strong>Recent Test Documents:</strong><br>
                        ${docs.map(doc => `ID: ${doc.id}, Message: ${doc.message}`).join('<br>')}
                    </div>
                `;
            } catch (error) {
                displayResult('firestore-status', `Firestore read failed: ${error.message}`, 'error');
            }
        }

        // Test Storage Upload
        async function testStorageUpload() {
            const fileInput = document.getElementById('file-input');
            const file = fileInput.files[0];
            
            if (!file) {
                displayResult('storage-status', 'Please select a file first', 'warning');
                return;
            }

            try {
                const storageRef = storage.ref(`test/${Date.now()}_${file.name}`);
                const uploadTask = storageRef.put(file);
                
                uploadTask.on('state_changed',
                    (snapshot) => {
                        const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
                        displayResult('storage-status', `Upload progress: ${progress.toFixed(1)}%`, 'warning');
                    },
                    (error) => {
                        displayResult('storage-status', `Upload failed: ${error.message}`, 'error');
                    },
                    async () => {
                        const downloadURL = await uploadTask.snapshot.ref.getDownloadURL();
                        displayResult('storage-status', 'Upload successful!', 'success');
                        document.getElementById('storage-results').innerHTML = `
                            <div class="success">
                                <strong>Upload Complete:</strong><br>
                                File: ${file.name}<br>
                                Size: ${(file.size / 1024).toFixed(1)} KB<br>
                                <a href="${downloadURL}" target="_blank">View File</a>
                            </div>
                        `;
                    }
                );
            } catch (error) {
                displayResult('storage-status', `Storage test failed: ${error.message}`, 'error');
            }
        }

        // Test Analytics
        function testAnalytics() {
            try {
                if (analytics) {
                    analytics.logEvent('firebase_test', {
                        test_type: 'configuration_test',
                        timestamp: Date.now()
                    });
                    displayResult('analytics-status', 'Analytics event logged successfully', 'success');
                } else {
                    displayResult('analytics-status', 'Analytics not available (this is normal in development)', 'warning');
                }
            } catch (error) {
                displayResult('analytics-status', `Analytics test failed: ${error.message}`, 'error');
            }
        }

        // Helper function to display results
        function displayResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="test-result ${type}">${message}</div>`;
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Starting Firebase configuration test...');
            initializeFirebase();
        });
    </script>
</body>
</html>
