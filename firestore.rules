rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Users Collection
    // Users can read/write their own data, others can read public profiles
    match /users/{userId} {
      allow read: if true; // Public profiles are readable by all
      allow write: if request.auth != null && request.auth.uid == userId;
      allow create: if request.auth != null && request.auth.uid == userId;
      allow update: if request.auth != null && request.auth.uid == userId;
      allow delete: if request.auth != null && 
        (request.auth.uid == userId || 
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin');
    }
    
    // Posts Collection
    // Posts are readable by all, writable by authenticated users
    match /posts/{postId} {
      allow read: if true; // All posts are public
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.authorId;
      allow update: if request.auth != null && 
        (request.auth.uid == resource.data.authorId || 
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin');
      allow delete: if request.auth != null && 
        (request.auth.uid == resource.data.authorId || 
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin');
    }
    
    // Comments Collection
    // Comments are readable by all, writable by authenticated users
    match /comments/{commentId} {
      allow read: if true; // All comments are public
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.authorId;
      allow update: if request.auth != null && 
        (request.auth.uid == resource.data.authorId || 
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin');
      allow delete: if request.auth != null && 
        (request.auth.uid == resource.data.authorId || 
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin');
    }
    
    // Notifications Collection
    // Users can only read/write their own notifications
    match /notifications/{notificationId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.userId;
      allow create: if request.auth != null;
    }
    
    // Follows Collection
    // Users can manage their own follow relationships
    match /follows/{followId} {
      allow read: if true; // Follow relationships are public
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.followerId;
      allow delete: if request.auth != null && 
        (request.auth.uid == resource.data.followerId || 
         request.auth.uid == resource.data.followingId);
    }
    
    // Likes Collection
    // Users can manage their own likes
    match /likes/{likeId} {
      allow read: if true; // Likes are public
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
      allow delete: if request.auth != null && 
        request.auth.uid == resource.data.userId;
    }
    
    // Reports Collection
    // Users can create reports, admins can read all
    match /reports/{reportId} {
      allow read: if request.auth != null && 
        (request.auth.uid == resource.data.reporterId || 
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin');
      allow create: if request.auth != null;
      allow update, delete: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // Admin Collection
    // Only admins can access admin documents
    match /admin/{document=**} {
      allow read, write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // Analytics Collection
    // Only admins can access analytics
    match /analytics/{document=**} {
      allow read, write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // Helper functions for validation
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return request.auth.uid == userId;
    }
    
    function isAdmin() {
      return isAuthenticated() && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    function isValidUser() {
      return isAuthenticated() && 
        exists(/databases/$(database)/documents/users/$(request.auth.uid));
    }
    
    function isValidPost(postData) {
      return postData.keys().hasAll(['authorId', 'content', 'createdAt']) &&
        postData.authorId == request.auth.uid &&
        postData.content is string &&
        postData.content.size() > 0 &&
        postData.content.size() <= 2000;
    }
    
    function isValidComment(commentData) {
      return commentData.keys().hasAll(['authorId', 'postId', 'content', 'createdAt']) &&
        commentData.authorId == request.auth.uid &&
        commentData.content is string &&
        commentData.content.size() > 0 &&
        commentData.content.size() <= 500;
    }
  }
}
