/**
 * Client Model for AI Voice Agent
 * Extends the existing user model for voice agent specific features
 */

import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';

const clientSchema = new mongoose.Schema({
  // Basic Information (from Google OAuth)
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  googleId: {
    type: String,
    unique: true,
    sparse: true
  },
  firstName: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  lastName: {
    type: String,
    trim: true,
    maxlength: 100
  },
  displayName: {
    type: String,
    trim: true,
    maxlength: 200
  },
  phone: {
    type: String,
    trim: true,
    match: [/^\+?[\d\s\-\(\)]+$/, 'Please enter a valid phone number']
  },
  profilePicture: {
    type: String,
    default: null
  },

  // Registration and Activity
  registrationDate: {
    type: Date,
    default: Date.now
  },
  lastLoginAt: {
    type: Date,
    default: Date.now
  },
  totalVisits: {
    type: Number,
    default: 0,
    min: 0
  },
  isActive: {
    type: Boolean,
    default: true
  },

  // Voice Agent Preferences
  voicePreferences: {
    enabled: {
      type: Boolean,
      default: true
    },
    voice: {
      type: String,
      default: 'en-US-Standard-A',
      enum: [
        'en-US-Standard-A', 'en-US-Standard-B', 'en-US-Standard-C', 'en-US-Standard-D',
        'en-US-Wavenet-A', 'en-US-Wavenet-B', 'en-US-Wavenet-C', 'en-US-Wavenet-D'
      ]
    },
    speed: {
      type: Number,
      default: 1.0,
      min: 0.5,
      max: 2.0
    },
    pitch: {
      type: Number,
      default: 1.0,
      min: 0.5,
      max: 2.0
    },
    volume: {
      type: Number,
      default: 0.8,
      min: 0.0,
      max: 1.0
    }
  },

  // Animation Preferences
  animationPreferences: {
    enabled: {
      type: Boolean,
      default: true
    },
    intensity: {
      type: Number,
      default: 0.3,
      min: 0.1,
      max: 1.0
    },
    color: {
      type: String,
      default: '#ff6b6b',
      match: [/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'Please enter a valid hex color']
    },
    reduceMotion: {
      type: Boolean,
      default: false
    }
  },

  // Personalization Data
  personalization: {
    preferredGreeting: {
      type: String,
      maxlength: 500
    },
    communicationStyle: {
      type: String,
      enum: ['formal', 'casual', 'friendly'],
      default: 'friendly'
    },
    languagePreference: {
      type: String,
      default: 'en-US'
    },
    timezone: {
      type: String,
      default: 'America/New_York'
    }
  },

  // Client History Summary (for quick access)
  clientSummary: {
    lastAppointmentDate: Date,
    lastServiceType: String,
    preferredServices: [String],
    totalAppointments: {
      type: Number,
      default: 0
    },
    averageRating: {
      type: Number,
      min: 1,
      max: 5
    },
    nextSuggestedDate: Date
  },

  // Conversation History Summary
  conversationSummary: {
    totalConversations: {
      type: Number,
      default: 0
    },
    averageSatisfaction: {
      type: Number,
      min: 1,
      max: 5
    },
    lastConversationDate: Date,
    preferredTopics: [String],
    commonQuestions: [String]
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
clientSchema.index({ email: 1 });
clientSchema.index({ googleId: 1 });
clientSchema.index({ lastLoginAt: -1 });
clientSchema.index({ isActive: 1 });
clientSchema.index({ 'clientSummary.nextSuggestedDate': 1 });

// Virtual for full name
clientSchema.virtual('fullName').get(function() {
  return this.displayName || `${this.firstName} ${this.lastName || ''}`.trim();
});

// Virtual for client type (new vs returning)
clientSchema.virtual('clientType').get(function() {
  return this.totalVisits === 0 ? 'new' : 'returning';
});

// Virtual for days since last visit
clientSchema.virtual('daysSinceLastVisit').get(function() {
  if (!this.clientSummary.lastAppointmentDate) return null;
  const now = new Date();
  const lastVisit = new Date(this.clientSummary.lastAppointmentDate);
  return Math.floor((now - lastVisit) / (1000 * 60 * 60 * 24));
});

// Methods
clientSchema.methods.updateLastLogin = function() {
  this.lastLoginAt = new Date();
  this.totalVisits += 1;
  return this.save();
};

clientSchema.methods.getPersonalizedGreeting = function() {
  if (this.personalization.preferredGreeting) {
    return this.personalization.preferredGreeting;
  }

  const name = this.firstName;
  const style = this.personalization.communicationStyle;

  if (this.clientType === 'new') {
    switch (style) {
      case 'formal':
        return `Good day, ${name}. Welcome to Barber Brothers Legacy. I see this is your first visit with us.`;
      case 'casual':
        return `Hey ${name}! Welcome to Barber Brothers Legacy. First time here?`;
      default:
        return `Welcome to Barber Brothers Legacy, ${name}! I'm excited to see this is your first visit with us.`;
    }
  } else {
    const daysSince = this.daysSinceLastVisit;
    const lastService = this.clientSummary.lastServiceType;

    switch (style) {
      case 'formal':
        return `Welcome back, ${name}. ${daysSince ? `It's been ${daysSince} days since your last visit` : 'Great to see you again'}${lastService ? ` for your ${lastService}` : ''}.`;
      case 'casual':
        return `Hey ${name}! ${daysSince ? `Been ${daysSince} days` : 'Good to see you again'}${lastService ? ` since that ${lastService}` : ''}. Ready for another great cut?`;
      default:
        return `Welcome back, ${name}! ${daysSince ? `It's been ${daysSince} days since your last visit` : 'So good to see you again'}${lastService ? ` for your ${lastService}` : ''}. How can I help you today?`;
    }
  }
};

clientSchema.methods.updateClientSummary = function(appointmentData) {
  this.clientSummary.lastAppointmentDate = appointmentData.appointmentDate;
  this.clientSummary.lastServiceType = appointmentData.serviceType;
  this.clientSummary.totalAppointments += 1;
  
  if (appointmentData.nextSuggestedDate) {
    this.clientSummary.nextSuggestedDate = appointmentData.nextSuggestedDate;
  }

  // Update preferred services
  if (!this.clientSummary.preferredServices.includes(appointmentData.serviceType)) {
    this.clientSummary.preferredServices.push(appointmentData.serviceType);
  }

  return this.save();
};

clientSchema.methods.updateConversationSummary = function(conversationData) {
  this.conversationSummary.totalConversations += 1;
  this.conversationSummary.lastConversationDate = new Date();
  
  if (conversationData.satisfaction) {
    const currentAvg = this.conversationSummary.averageSatisfaction || 0;
    const totalConversations = this.conversationSummary.totalConversations;
    this.conversationSummary.averageSatisfaction = 
      ((currentAvg * (totalConversations - 1)) + conversationData.satisfaction) / totalConversations;
  }

  return this.save();
};

// Static methods
clientSchema.statics.findByEmail = function(email) {
  return this.findOne({ email: email.toLowerCase() });
};

clientSchema.statics.findByGoogleId = function(googleId) {
  return this.findOne({ googleId });
};

clientSchema.statics.getActiveClients = function() {
  return this.find({ isActive: true });
};

clientSchema.statics.getClientsNeedingFollowUp = function() {
  const today = new Date();
  return this.find({
    isActive: true,
    'clientSummary.nextSuggestedDate': { $lte: today }
  });
};

// Pre-save middleware
clientSchema.pre('save', function(next) {
  // Ensure displayName is set
  if (!this.displayName) {
    this.displayName = `${this.firstName} ${this.lastName || ''}`.trim();
  }
  
  next();
});

const Client = mongoose.model('Client', clientSchema);

export default Client;
