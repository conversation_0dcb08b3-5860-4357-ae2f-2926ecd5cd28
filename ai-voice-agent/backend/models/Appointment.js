/**
 * Appointment Model for AI Voice Agent
 * Stores appointment history for contextual conversations
 */

import mongoose from 'mongoose';

const appointmentSchema = new mongoose.Schema({
  // Client Reference
  clientId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Client',
    required: true,
    index: true
  },

  // Appointment Details
  serviceType: {
    type: String,
    required: true,
    enum: [
      'Classic Haircut',
      'Beard Trim',
      'Hot Towel Shave',
      'Hair Styling',
      'Consultation',
      'Full Service',
      'Touch Up',
      'Special Event Styling'
    ]
  },
  serviceCategory: {
    type: String,
    enum: ['haircut', 'beard', 'styling', 'consultation', 'other'],
    default: 'haircut'
  },
  appointmentDate: {
    type: Date,
    required: true,
    index: true
  },
  durationMinutes: {
    type: Number,
    default: 60,
    min: 15,
    max: 240
  },
  status: {
    type: String,
    enum: ['scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show'],
    default: 'scheduled',
    index: true
  },

  // Service Details
  stylistName: {
    type: String,
    default: 'Andre The Barber'
  },
  price: {
    type: Number,
    min: 0
  },
  tipAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  totalAmount: {
    type: Number,
    min: 0
  },

  // Service Specifics
  serviceDetails: {
    haircutStyle: String,
    hairLengthBefore: {
      type: String,
      enum: ['very_short', 'short', 'medium', 'long', 'very_long']
    },
    hairLengthAfter: {
      type: String,
      enum: ['very_short', 'short', 'medium', 'long', 'very_long']
    },
    specialRequests: String,
    productsUsed: [{
      name: String,
      brand: String,
      type: {
        type: String,
        enum: ['shampoo', 'conditioner', 'styling_gel', 'pomade', 'oil', 'spray', 'other']
      }
    }],
    techniques: [{
      type: String,
      enum: ['scissor_cut', 'clipper_cut', 'razor_cut', 'texturizing', 'layering', 'fade', 'taper']
    }]
  },

  // Follow-up Information
  followUp: {
    nextSuggestedDate: {
      type: Date,
      index: true
    },
    suggestedServices: [String],
    maintenanceTips: [String],
    productRecommendations: [{
      name: String,
      reason: String,
      priority: {
        type: String,
        enum: ['low', 'medium', 'high'],
        default: 'medium'
      }
    }]
  },

  // Client Feedback
  feedback: {
    satisfaction: {
      type: Number,
      min: 1,
      max: 5
    },
    wouldRecommend: Boolean,
    comments: String,
    specificPraise: [String],
    improvementSuggestions: [String]
  },

  // Stylist Notes
  stylistNotes: {
    beforeService: String,
    duringService: String,
    afterService: String,
    clientPreferences: [String],
    challengesEncountered: [String],
    successfulTechniques: [String]
  },

  // Media
  media: {
    beforePhotos: [String],  // URLs to before photos
    afterPhotos: [String],   // URLs to after photos
    processPhotos: [String], // URLs to process photos
    videoClips: [String]     // URLs to video clips
  },

  // Booking Information
  bookingDetails: {
    bookedVia: {
      type: String,
      enum: ['voice_agent', 'website', 'phone', 'walk_in', 'social_media'],
      default: 'website'
    },
    bookingDate: {
      type: Date,
      default: Date.now
    },
    remindersSent: {
      type: Number,
      default: 0
    },
    lastReminderDate: Date,
    confirmationStatus: {
      type: String,
      enum: ['pending', 'confirmed', 'declined'],
      default: 'pending'
    }
  },

  // Weather and Context (for conversation)
  contextData: {
    weather: String,
    seasonalNotes: String,
    specialOccasion: String,
    clientMood: {
      type: String,
      enum: ['excited', 'nervous', 'relaxed', 'rushed', 'celebratory', 'professional']
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
appointmentSchema.index({ clientId: 1, appointmentDate: -1 });
appointmentSchema.index({ status: 1, appointmentDate: 1 });
appointmentSchema.index({ 'followUp.nextSuggestedDate': 1 });
appointmentSchema.index({ serviceType: 1 });
appointmentSchema.index({ 'bookingDetails.bookedVia': 1 });

// Virtual for appointment status display
appointmentSchema.virtual('statusDisplay').get(function() {
  const statusMap = {
    'scheduled': 'Scheduled',
    'confirmed': 'Confirmed',
    'in_progress': 'In Progress',
    'completed': 'Completed',
    'cancelled': 'Cancelled',
    'no_show': 'No Show'
  };
  return statusMap[this.status] || this.status;
});

// Virtual for days until appointment
appointmentSchema.virtual('daysUntilAppointment').get(function() {
  if (this.status !== 'scheduled' && this.status !== 'confirmed') return null;
  const now = new Date();
  const appointmentDate = new Date(this.appointmentDate);
  return Math.ceil((appointmentDate - now) / (1000 * 60 * 60 * 24));
});

// Virtual for days since appointment
appointmentSchema.virtual('daysSinceAppointment').get(function() {
  if (this.status !== 'completed') return null;
  const now = new Date();
  const appointmentDate = new Date(this.appointmentDate);
  return Math.floor((now - appointmentDate) / (1000 * 60 * 60 * 24));
});

// Virtual for is overdue for next appointment
appointmentSchema.virtual('isOverdueForNext').get(function() {
  if (!this.followUp.nextSuggestedDate) return false;
  const now = new Date();
  return new Date(this.followUp.nextSuggestedDate) < now;
});

// Methods
appointmentSchema.methods.calculateNextSuggestedDate = function() {
  const baseDate = this.appointmentDate;
  let weeksToAdd = 4; // Default 4 weeks

  // Adjust based on service type
  switch (this.serviceType) {
    case 'Classic Haircut':
      weeksToAdd = 4;
      break;
    case 'Beard Trim':
      weeksToAdd = 2;
      break;
    case 'Touch Up':
      weeksToAdd = 6;
      break;
    case 'Full Service':
      weeksToAdd = 3;
      break;
    default:
      weeksToAdd = 4;
  }

  // Adjust based on hair length after
  if (this.serviceDetails.hairLengthAfter) {
    switch (this.serviceDetails.hairLengthAfter) {
      case 'very_short':
        weeksToAdd += 1;
        break;
      case 'short':
        weeksToAdd += 0;
        break;
      case 'medium':
        weeksToAdd -= 1;
        break;
      case 'long':
        weeksToAdd -= 2;
        break;
    }
  }

  const nextDate = new Date(baseDate);
  nextDate.setDate(nextDate.getDate() + (weeksToAdd * 7));
  
  this.followUp.nextSuggestedDate = nextDate;
  return nextDate;
};

appointmentSchema.methods.generateConversationContext = function() {
  const context = {
    lastVisit: {
      date: this.appointmentDate,
      service: this.serviceType,
      satisfaction: this.feedback.satisfaction,
      daysSince: this.daysSinceAppointment
    },
    preferences: {
      style: this.serviceDetails.haircutStyle,
      techniques: this.serviceDetails.techniques,
      products: this.serviceDetails.productsUsed
    },
    followUp: {
      suggested: this.followUp.nextSuggestedDate,
      isOverdue: this.isOverdueForNext,
      recommendations: this.followUp.productRecommendations
    }
  };

  return context;
};

appointmentSchema.methods.updateFromVoiceBooking = function(bookingData) {
  this.bookingDetails.bookedVia = 'voice_agent';
  this.bookingDetails.bookingDate = new Date();
  
  if (bookingData.serviceType) {
    this.serviceType = bookingData.serviceType;
  }
  
  if (bookingData.appointmentDate) {
    this.appointmentDate = new Date(bookingData.appointmentDate);
  }
  
  if (bookingData.specialRequests) {
    this.serviceDetails.specialRequests = bookingData.specialRequests;
  }

  return this.save();
};

// Static methods
appointmentSchema.statics.findByClient = function(clientId) {
  return this.find({ clientId }).sort({ appointmentDate: -1 });
};

appointmentSchema.statics.findUpcoming = function(clientId) {
  const now = new Date();
  return this.find({
    clientId,
    appointmentDate: { $gte: now },
    status: { $in: ['scheduled', 'confirmed'] }
  }).sort({ appointmentDate: 1 });
};

appointmentSchema.statics.findRecent = function(clientId, limit = 5) {
  return this.find({
    clientId,
    status: 'completed'
  })
  .sort({ appointmentDate: -1 })
  .limit(limit)
  .populate('clientId', 'firstName lastName email');
};

appointmentSchema.statics.findOverdueClients = function() {
  const now = new Date();
  return this.find({
    status: 'completed',
    'followUp.nextSuggestedDate': { $lt: now }
  })
  .populate('clientId', 'firstName lastName email voicePreferences')
  .sort({ 'followUp.nextSuggestedDate': 1 });
};

appointmentSchema.statics.getServiceStats = function() {
  return this.aggregate([
    { $match: { status: 'completed' } },
    { $group: {
      _id: '$serviceType',
      count: { $sum: 1 },
      avgSatisfaction: { $avg: '$feedback.satisfaction' },
      avgPrice: { $avg: '$price' }
    }},
    { $sort: { count: -1 } }
  ]);
};

// Pre-save middleware
appointmentSchema.pre('save', function(next) {
  // Calculate total amount if not set
  if (this.price && !this.totalAmount) {
    this.totalAmount = this.price + (this.tipAmount || 0);
  }

  // Calculate next suggested date if not set and appointment is completed
  if (this.status === 'completed' && !this.followUp.nextSuggestedDate) {
    this.calculateNextSuggestedDate();
  }

  next();
});

const Appointment = mongoose.model('Appointment', appointmentSchema);

export default Appointment;
