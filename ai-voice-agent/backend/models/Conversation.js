/**
 * Conversation Model for AI Voice Agent
 * Logs voice agent conversations for improvement and personalization
 */

import mongoose from 'mongoose';

const messageSchema = new mongoose.Schema({
  timestamp: {
    type: Date,
    default: Date.now
  },
  speaker: {
    type: String,
    enum: ['agent', 'user'],
    required: true
  },
  content: {
    type: String,
    required: true
  },
  intent: String,
  confidence: {
    type: Number,
    min: 0,
    max: 1
  },
  synthesisTimeMs: Number,
  animationSynced: {
    type: Boolean,
    default: false
  }
}, { _id: false });

const conversationSchema = new mongoose.Schema({
  // Client Reference
  clientId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Client',
    required: true,
    index: true
  },
  sessionId: {
    type: String,
    required: true,
    index: true
  },

  // Conversation Details
  conversationType: {
    type: String,
    enum: ['welcome', 'booking', 'inquiry', 'follow_up', 'general', 'support'],
    default: 'welcome',
    index: true
  },
  triggerEvent: {
    type: String,
    enum: ['login', 'manual', 'scheduled', 'callback', 'button_click'],
    default: 'login'
  },
  conversationStart: {
    type: Date,
    default: Date.now,
    index: true
  },
  conversationEnd: Date,
  durationSeconds: Number,

  // Conversation Content
  messages: [messageSchema],
  conversationSummary: String,
  clientIntent: String,
  detectedMood: {
    type: String,
    enum: ['happy', 'neutral', 'frustrated', 'excited', 'confused', 'satisfied']
  },

  // Interaction Metrics
  metrics: {
    userInterruptions: {
      type: Number,
      default: 0
    },
    agentRestarts: {
      type: Number,
      default: 0
    },
    totalMessages: {
      type: Number,
      default: 0
    },
    averageResponseTime: Number,
    voiceSynthesisTime: Number,
    animationSyncQuality: {
      type: Number,
      min: 0,
      max: 1
    }
  },

  // Conversation Outcomes
  outcomes: {
    successfulCompletion: {
      type: Boolean,
      default: false
    },
    userSatisfaction: {
      type: Number,
      min: 1,
      max: 5
    },
    appointmentBooked: {
      type: Boolean,
      default: false
    },
    appointmentId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Appointment'
    },
    followUpRequired: {
      type: Boolean,
      default: false
    },
    followUpDate: Date,
    goalAchieved: {
      type: Boolean,
      default: false
    },
    conversionType: {
      type: String,
      enum: ['booking', 'information', 'support', 'engagement', 'none'],
      default: 'none'
    }
  },

  // Technical Performance
  technical: {
    voiceSynthesisTimeMs: Number,
    animationSyncSuccess: {
      type: Boolean,
      default: true
    },
    errorsEncountered: [{
      timestamp: Date,
      errorType: String,
      errorMessage: String,
      severity: {
        type: String,
        enum: ['low', 'medium', 'high', 'critical'],
        default: 'medium'
      }
    }],
    performanceScore: {
      type: Number,
      min: 0,
      max: 100
    }
  },

  // Context and Environment
  context: {
    deviceType: {
      type: String,
      enum: ['desktop', 'mobile', 'tablet'],
      default: 'desktop'
    },
    browser: String,
    location: {
      country: String,
      region: String,
      city: String
    },
    timeOfDay: {
      type: String,
      enum: ['morning', 'afternoon', 'evening', 'night']
    },
    dayOfWeek: {
      type: String,
      enum: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
    },
    isBusinessHours: {
      type: Boolean,
      default: true
    }
  },

  // Preferences and Learning
  preferencesUpdated: {
    voiceSettings: {
      speed: Number,
      pitch: Number,
      volume: Number,
      voice: String
    },
    animationSettings: {
      enabled: Boolean,
      intensity: Number,
      color: String
    },
    communicationStyle: String,
    topics: [String]
  },

  // Analytics Tags
  tags: [String],
  
  // Quality Assurance
  qualityReview: {
    reviewed: {
      type: Boolean,
      default: false
    },
    reviewedBy: String,
    reviewDate: Date,
    qualityScore: {
      type: Number,
      min: 0,
      max: 100
    },
    improvementNotes: String
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
conversationSchema.index({ clientId: 1, conversationStart: -1 });
conversationSchema.index({ sessionId: 1 });
conversationSchema.index({ conversationType: 1, conversationStart: -1 });
conversationSchema.index({ 'outcomes.successfulCompletion': 1 });
conversationSchema.index({ 'outcomes.appointmentBooked': 1 });
conversationSchema.index({ 'context.timeOfDay': 1, 'context.dayOfWeek': 1 });

// Virtual for conversation duration in minutes
conversationSchema.virtual('durationMinutes').get(function() {
  if (!this.durationSeconds) return null;
  return Math.round(this.durationSeconds / 60 * 100) / 100;
});

// Virtual for conversation quality score
conversationSchema.virtual('qualityScore').get(function() {
  let score = 50; // Base score

  // Successful completion bonus
  if (this.outcomes.successfulCompletion) score += 20;
  
  // User satisfaction bonus
  if (this.outcomes.userSatisfaction) {
    score += (this.outcomes.userSatisfaction - 3) * 10;
  }
  
  // Goal achievement bonus
  if (this.outcomes.goalAchieved) score += 15;
  
  // Technical performance
  if (this.technical.animationSyncSuccess) score += 5;
  
  // Penalty for interruptions and restarts
  score -= (this.metrics.userInterruptions * 2);
  score -= (this.metrics.agentRestarts * 5);
  
  // Penalty for errors
  score -= (this.technical.errorsEncountered.length * 3);

  return Math.max(0, Math.min(100, score));
});

// Virtual for is recent conversation
conversationSchema.virtual('isRecent').get(function() {
  const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
  return this.conversationStart > oneDayAgo;
});

// Methods
conversationSchema.methods.addMessage = function(speaker, content, additionalData = {}) {
  const message = {
    timestamp: new Date(),
    speaker,
    content,
    ...additionalData
  };
  
  this.messages.push(message);
  this.metrics.totalMessages = this.messages.length;
  
  return this.save();
};

conversationSchema.methods.endConversation = function(outcomes = {}) {
  this.conversationEnd = new Date();
  this.durationSeconds = Math.floor((this.conversationEnd - this.conversationStart) / 1000);
  
  // Update outcomes
  Object.assign(this.outcomes, outcomes);
  
  // Calculate performance metrics
  this.calculatePerformanceMetrics();
  
  return this.save();
};

conversationSchema.methods.calculatePerformanceMetrics = function() {
  const messages = this.messages;
  
  if (messages.length > 0) {
    // Calculate average response time
    const responseTimes = [];
    for (let i = 1; i < messages.length; i++) {
      if (messages[i].speaker === 'agent' && messages[i-1].speaker === 'user') {
        const responseTime = messages[i].timestamp - messages[i-1].timestamp;
        responseTimes.push(responseTime);
      }
    }
    
    if (responseTimes.length > 0) {
      this.metrics.averageResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
    }
    
    // Calculate voice synthesis time
    const synthesisTimes = messages
      .filter(m => m.speaker === 'agent' && m.synthesisTimeMs)
      .map(m => m.synthesisTimeMs);
    
    if (synthesisTimes.length > 0) {
      this.metrics.voiceSynthesisTime = synthesisTimes.reduce((a, b) => a + b, 0) / synthesisTimes.length;
    }
    
    // Calculate animation sync quality
    const syncedMessages = messages.filter(m => m.speaker === 'agent');
    const successfulSyncs = syncedMessages.filter(m => m.animationSynced).length;
    
    if (syncedMessages.length > 0) {
      this.metrics.animationSyncQuality = successfulSyncs / syncedMessages.length;
    }
  }
  
  // Calculate overall performance score
  this.technical.performanceScore = this.qualityScore;
};

conversationSchema.methods.generateSummary = function() {
  const messages = this.messages;
  const userMessages = messages.filter(m => m.speaker === 'user');
  const agentMessages = messages.filter(m => m.speaker === 'agent');
  
  let summary = `Conversation with ${this.clientId} lasted ${this.durationMinutes} minutes. `;
  
  if (this.outcomes.appointmentBooked) {
    summary += 'Successfully booked an appointment. ';
  }
  
  if (this.outcomes.userSatisfaction) {
    summary += `User satisfaction: ${this.outcomes.userSatisfaction}/5. `;
  }
  
  if (this.clientIntent) {
    summary += `Primary intent: ${this.clientIntent}. `;
  }
  
  this.conversationSummary = summary.trim();
  return summary;
};

// Static methods
conversationSchema.statics.findByClient = function(clientId, limit = 10) {
  return this.find({ clientId })
    .sort({ conversationStart: -1 })
    .limit(limit)
    .populate('clientId', 'firstName lastName email');
};

conversationSchema.statics.findSuccessful = function(timeframe = 7) {
  const startDate = new Date(Date.now() - timeframe * 24 * 60 * 60 * 1000);
  return this.find({
    conversationStart: { $gte: startDate },
    'outcomes.successfulCompletion': true
  });
};

conversationSchema.statics.getAnalytics = function(timeframe = 30) {
  const startDate = new Date(Date.now() - timeframe * 24 * 60 * 60 * 1000);
  
  return this.aggregate([
    { $match: { conversationStart: { $gte: startDate } } },
    { $group: {
      _id: null,
      totalConversations: { $sum: 1 },
      successfulConversations: {
        $sum: { $cond: ['$outcomes.successfulCompletion', 1, 0] }
      },
      appointmentsBooked: {
        $sum: { $cond: ['$outcomes.appointmentBooked', 1, 0] }
      },
      averageDuration: { $avg: '$durationSeconds' },
      averageSatisfaction: { $avg: '$outcomes.userSatisfaction' },
      averageQuality: { $avg: '$technical.performanceScore' }
    }}
  ]);
};

conversationSchema.statics.getConversationsByType = function(timeframe = 30) {
  const startDate = new Date(Date.now() - timeframe * 24 * 60 * 60 * 1000);
  
  return this.aggregate([
    { $match: { conversationStart: { $gte: startDate } } },
    { $group: {
      _id: '$conversationType',
      count: { $sum: 1 },
      successRate: {
        $avg: { $cond: ['$outcomes.successfulCompletion', 1, 0] }
      },
      averageDuration: { $avg: '$durationSeconds' }
    }},
    { $sort: { count: -1 } }
  ]);
};

// Pre-save middleware
conversationSchema.pre('save', function(next) {
  // Set context data if not already set
  if (!this.context.timeOfDay) {
    const hour = this.conversationStart.getHours();
    if (hour < 6) this.context.timeOfDay = 'night';
    else if (hour < 12) this.context.timeOfDay = 'morning';
    else if (hour < 18) this.context.timeOfDay = 'afternoon';
    else this.context.timeOfDay = 'evening';
  }
  
  if (!this.context.dayOfWeek) {
    const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    this.context.dayOfWeek = days[this.conversationStart.getDay()];
  }
  
  // Generate summary if conversation is ended and no summary exists
  if (this.conversationEnd && !this.conversationSummary) {
    this.generateSummary();
  }
  
  next();
});

const Conversation = mongoose.model('Conversation', conversationSchema);

export default Conversation;
