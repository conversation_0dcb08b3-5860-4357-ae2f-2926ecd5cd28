# AI Voice Agent Environment Configuration
# Copy this file to .env and update with your actual values

# Development Environment
NODE_ENV=development
PORT=3001
FRONTEND_URL=http://localhost:5173

# Database Configuration (MongoDB)
MONGODB_URI=mongodb://localhost:27017/ai-voice-agent
# For production, use MongoDB Atlas or your preferred MongoDB service
# MONGODB_URI=mongodb+srv://username:<EMAIL>/ai-voice-agent

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here-make-it-long-and-random
JWT_EXPIRES_IN=7d
REFRESH_TOKEN_SECRET=your-refresh-token-secret-here
REFRESH_TOKEN_EXPIRES_IN=30d

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_CALLBACK_URL=http://localhost:3001/api/auth/google/callback

# Voice and Speech Configuration
# Text-to-Speech Service (optional premium service)
TTS_SERVICE_API_KEY=your-tts-service-api-key
TTS_SERVICE_URL=https://api.your-tts-service.com

# Voice Configuration
DEFAULT_VOICE_RATE=1.0
DEFAULT_VOICE_PITCH=1.0
DEFAULT_VOICE_VOLUME=0.8
VOICE_LANGUAGE=en-US

# Animation Configuration
ANIMATION_FRAME_RATE=60
PULSE_DURATION=1000
PULSE_INTENSITY=0.3

# Security Configuration
CORS_ORIGIN=http://localhost:5173
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/ai-voice-agent.log

# Performance Configuration
ENABLE_COMPRESSION=true
ENABLE_CACHING=true
CACHE_TTL=3600

# Feature Flags
ENABLE_VOICE_AGENT=true
ENABLE_ANIMATION=true
ENABLE_ANALYTICS=false
ENABLE_DEBUG_MODE=true

# Integration Configuration
# Barber Brothers Legacy Website Integration
MAIN_WEBSITE_URL=https://www.barberbrotherz.com
COMMUNITY_HUB_URL=http://localhost:3000

# Appointment System Integration
APPOINTMENT_API_URL=http://localhost:3000/api
APPOINTMENT_API_KEY=your-appointment-api-key

# Analytics (optional)
GOOGLE_ANALYTICS_ID=your-ga-tracking-id
MIXPANEL_TOKEN=your-mixpanel-token

# Error Reporting (optional)
SENTRY_DSN=your-sentry-dsn

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Development Tools
ENABLE_HOT_RELOAD=true
ENABLE_SOURCE_MAPS=true
ENABLE_MOCK_DATA=true
