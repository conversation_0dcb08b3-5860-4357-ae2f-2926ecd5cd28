# VS Code Setup Guide for AI Voice Agent Development

## 🚀 Quick Start

This guide will help you set up Visual Studio Code for optimal AI Voice Agent development with AI coding assistants.

## 📋 Prerequisites

- **Visual Studio Code** (latest version)
- **Node.js** 16+ and npm 8+
- **Git** for version control
- **Chrome/Edge** for debugging

## 🔧 Essential Extensions

### AI Coding Assistants (Choose One or More)

1. **GitHub Copilot** (Recommended)
   ```
   ext install GitHub.copilot
   ext install GitHub.copilot-chat
   ```

2. **Continue** (Open Source Alternative)
   ```
   ext install continue.continue
   ```

3. **Cursor AI** (If using Cursor editor)
   - Download from [cursor.sh](https://cursor.sh)

### Core Development Extensions

```bash
# Essential formatting and linting
ext install esbenp.prettier-vscode
ext install dbaeumer.vscode-eslint

# JavaScript/Node.js development
ext install ms-vscode.vscode-typescript-next
ext install christian-kohler.npm-intellisense
ext install christian-kohler.path-intellisense

# Git integration
ext install eamodio.gitlens
ext install mhutchie.git-graph

# Testing and debugging
ext install ms-vscode.vscode-jest
ext install ms-vscode.vscode-js-debug

# Web development
ext install ritwickdey.liveserver
ext install formulahendry.auto-close-tag
ext install pranaygp.vscode-css-peek

# Productivity
ext install pkief.material-icon-theme
ext install alefragnani.bookmarks
ext install oderwat.indent-rainbow
```

## ⚙️ Workspace Configuration

The project includes pre-configured VS Code settings in `.vscode/`:

- **settings.json**: Optimized editor settings for AI-assisted development
- **extensions.json**: Recommended extensions list
- **tasks.json**: Pre-defined build and test tasks
- **launch.json**: Debug configurations for frontend and backend

## 🤖 AI Assistant Configuration

### GitHub Copilot Setup

1. Install the GitHub Copilot extension
2. Sign in with your GitHub account
3. Configure settings in VS Code:

```json
{
  "github.copilot.enable": {
    "*": true,
    "yaml": false,
    "plaintext": false,
    "markdown": true
  },
  "github.copilot.inlineSuggest.enable": true,
  "github.copilot.chat.localeOverride": "en"
}
```

### Continue Setup (Open Source)

1. Install the Continue extension
2. Configure your preferred AI model:

```json
{
  "continue.telemetryEnabled": false,
  "continue.enableTabAutocomplete": true
}
```

## 🎯 Development Workflow

### 1. Opening the Project

```bash
# Clone the repository
git clone https://github.com/andreb17/dre1z78.git
cd dre1z78/ai-voice-agent

# Open in VS Code
code .
```

### 2. Initial Setup

1. **Install Dependencies**: Use `Ctrl+Shift+P` → "Tasks: Run Task" → "Install Dependencies"
2. **Configure Environment**: Copy `.env.example` to `.env` and update values
3. **Start Development**: Use `Ctrl+Shift+P` → "Tasks: Run Task" → "Start Development Server"

### 3. Using AI Assistants

#### With GitHub Copilot:
- **Inline Suggestions**: Type code and accept suggestions with `Tab`
- **Chat**: Use `Ctrl+Shift+I` to open Copilot Chat
- **Explain Code**: Select code and use `Ctrl+Shift+P` → "GitHub Copilot: Explain This"

#### With Continue:
- **Chat**: Use `Ctrl+Shift+M` to open Continue chat
- **Edit Code**: Select code and use `Ctrl+Shift+L` for inline editing
- **Generate Tests**: Use `/test` command in chat

## 🐛 Debugging Configuration

### Frontend Debugging (Chrome)
- **Launch**: `F5` → "Launch Frontend (Chrome)"
- **Breakpoints**: Set breakpoints in `.js` files
- **Console**: Use browser DevTools integrated with VS Code

### Backend Debugging (Node.js)
- **Launch**: `F5` → "Launch Backend (Node.js)"
- **Breakpoints**: Set breakpoints in backend `.js` files
- **Variables**: Inspect variables in VS Code Debug panel

### Voice Agent Specific Debugging
- **Voice Testing**: `F5` → "Debug Voice Agent Component"
- **Animation Performance**: `F5` → "Debug Animation Performance"

## 🧪 Testing Integration

### Running Tests
- **All Tests**: `Ctrl+Shift+P` → "Tasks: Run Task" → "Run Tests"
- **Watch Mode**: `Ctrl+Shift+P` → "Tasks: Run Task" → "Watch Tests"
- **Current File**: `F5` → "Debug Current Test File"

### Test Coverage
- **Coverage Report**: `Ctrl+Shift+P` → "Tasks: Run Task" → "Run Tests with Coverage"
- **View Coverage**: Open `coverage/lcov-report/index.html`

## 🎨 Code Formatting

### Automatic Formatting
- **On Save**: Enabled by default
- **Manual**: `Shift+Alt+F`
- **Selection**: Select code → `Shift+Alt+F`

### Linting
- **Auto-fix**: `Ctrl+Shift+P` → "ESLint: Fix all auto-fixable Problems"
- **Manual**: `Ctrl+Shift+P` → "Tasks: Run Task" → "Lint Code"

## 🚀 Performance Tips

### VS Code Optimization
```json
{
  "files.watcherExclude": {
    "**/node_modules/**": true,
    "**/dist/**": true,
    "**/coverage/**": true
  },
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/coverage": true
  }
}
```

### AI Assistant Performance
- **Disable in Large Files**: AI suggestions work best in files < 1000 lines
- **Use Specific Prompts**: Be specific in chat prompts for better results
- **Context Awareness**: Keep related files open for better AI context

## 🔧 Troubleshooting

### Common Issues

1. **Extensions Not Loading**
   ```bash
   # Reset VS Code extensions
   code --disable-extensions
   code --enable-proposed-api
   ```

2. **AI Assistant Not Working**
   - Check internet connection
   - Verify authentication (GitHub/API keys)
   - Restart VS Code

3. **Debugging Issues**
   - Ensure ports 5173 (frontend) and 3001 (backend) are available
   - Check `.vscode/launch.json` configuration
   - Verify source maps are enabled

4. **Performance Issues**
   - Disable unnecessary extensions
   - Increase VS Code memory limit
   - Use workspace-specific settings

### Getting Help

- **VS Code Issues**: [VS Code GitHub](https://github.com/microsoft/vscode/issues)
- **Extension Issues**: Check individual extension repositories
- **Project Issues**: [Project GitHub Issues](https://github.com/andreb17/dre1z78/issues)

## 📚 Additional Resources

- [VS Code Documentation](https://code.visualstudio.com/docs)
- [GitHub Copilot Documentation](https://docs.github.com/en/copilot)
- [Continue Documentation](https://continue.dev/docs)
- [Node.js Debugging Guide](https://code.visualstudio.com/docs/nodejs/nodejs-debugging)

## 🎯 Next Steps

1. **Complete Setup**: Follow this guide to configure your environment
2. **Explore Features**: Try AI-assisted coding with voice agent components
3. **Run Tests**: Ensure everything works with the test suite
4. **Start Developing**: Begin implementing voice agent features

---

**Happy Coding with AI Assistance!** 🤖✨
