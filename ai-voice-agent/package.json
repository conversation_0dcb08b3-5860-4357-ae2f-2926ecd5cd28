{"name": "ai-voice-agent", "version": "1.0.0", "description": "AI Voice Agent for Barber Brothers Legacy - Personalized voice assistance with speech-synchronized animations", "main": "src/index.js", "type": "module", "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "vite", "dev:backend": "nodemon backend/server.js", "build": "vite build", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest tests/unit", "test:integration": "jest tests/integration", "test:performance": "jest tests/performance", "lint": "eslint src/ backend/ --ext .js", "lint:fix": "eslint src/ backend/ --ext .js --fix", "format": "prettier --write \"src/**/*.{js,css,html}\" \"backend/**/*.js\"", "start": "node backend/server.js", "build:docs": "jsdoc -c jsdoc.conf.json"}, "keywords": ["ai", "voice-agent", "speech-synthesis", "animation", "barbershop", "o<PERSON>h", "personalization", "accessibility"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/andreb17/dre1z78.git", "directory": "ai-voice-agent"}, "bugs": {"url": "https://github.com/andreb17/dre1z78/issues"}, "homepage": "https://www.barberbrotherz.com", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "mongoose": "^7.6.3", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"vite": "^4.5.0", "concurrently": "^8.2.2", "nodemon": "^3.0.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "eslint": "^8.53.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "prettier": "^3.0.3", "jsdoc": "^4.0.2", "supertest": "^6.3.3", "@testing-library/jest-dom": "^6.1.4", "@testing-library/dom": "^9.3.3"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/tests/setup.js"], "collectCoverageFrom": ["src/**/*.js", "backend/**/*.js", "!src/index.js", "!backend/server.js"], "coverageThreshold": {"global": {"branches": 80, "functions": 80, "lines": 80, "statements": 80}}}, "eslintConfig": {"extends": ["airbnb-base"], "env": {"browser": true, "node": true, "jest": true, "es2022": true}, "parserOptions": {"ecmaVersion": 2022, "sourceType": "module"}, "rules": {"no-console": "warn", "no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "import/extensions": ["error", "ignorePackages"]}}, "prettier": {"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 100, "tabWidth": 2, "useTabs": false}}