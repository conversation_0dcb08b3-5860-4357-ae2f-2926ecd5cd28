/**
 * PulsingBall - Speech-synchronized animation component
 * Creates a smooth pulsing ball that syncs with AI agent speech
 */

export class PulsingBall {
  constructor(options = {}) {
    this.options = {
      container: document.body,
      size: 30,
      color: '#ff6b6b',
      intensity: 0.3,
      position: 'bottom-right',
      zIndex: 9999,
      enableGlow: true,
      enableParticles: false,
      respectMotionPreference: true,
      ...options
    };

    // Animation state
    this.state = {
      isVisible: false,
      isPulsing: false,
      isAnimating: false,
      currentScale: 1,
      targetScale: 1,
      animationId: null,
      pulseStartTime: null
    };

    // Animation settings
    this.animation = {
      baseScale: 1,
      maxScale: 1 + this.options.intensity,
      duration: 1000,
      easing: 'easeInOutSine',
      frameRate: 60,
      waveformData: null,
      syncOffset: 0
    };

    // DOM elements
    this.container = null;
    this.ballElement = null;
    this.glowElement = null;
    this.particleElements = [];

    // Performance monitoring
    this.performance = {
      frameCount: 0,
      lastFrameTime: 0,
      averageFPS: 60,
      droppedFrames: 0
    };

    // Event listeners
    this.eventListeners = new Map();

    // Initialize
    this.init();
  }

  /**
   * Initialize the pulsing ball
   */
  init() {
    try {
      console.log('🎯 Initializing Pulsing Ball...');
      
      // Check motion preferences
      if (this.options.respectMotionPreference && this.prefersReducedMotion()) {
        console.log('⚠️ Reduced motion preferred, disabling animations');
        this.options.intensity = 0.1;
        this.animation.maxScale = 1.05;
      }
      
      // Create DOM elements
      this.createElements();
      
      // Setup event listeners
      this.setupEventListeners();
      
      console.log('✅ Pulsing Ball initialized successfully');
      
    } catch (error) {
      console.error('❌ Failed to initialize Pulsing Ball:', error);
      throw error;
    }
  }

  /**
   * Create DOM elements
   */
  createElements() {
    // Create container
    this.container = document.createElement('div');
    this.container.className = 'voice-agent-pulsing-ball-container';
    this.container.style.cssText = this.getContainerStyles();
    
    // Create main ball element
    this.ballElement = document.createElement('div');
    this.ballElement.className = 'voice-agent-pulsing-ball';
    this.ballElement.style.cssText = this.getBallStyles();
    
    // Create glow effect
    if (this.options.enableGlow) {
      this.glowElement = document.createElement('div');
      this.glowElement.className = 'voice-agent-pulsing-ball-glow';
      this.glowElement.style.cssText = this.getGlowStyles();
      this.container.appendChild(this.glowElement);
    }
    
    // Add ball to container
    this.container.appendChild(this.ballElement);
    
    // Create particles if enabled
    if (this.options.enableParticles) {
      this.createParticles();
    }
    
    // Add to DOM
    const targetContainer = this.options.container || document.body;
    targetContainer.appendChild(this.container);
    
    // Initially hidden
    this.hide();
  }

  /**
   * Get container CSS styles
   */
  getContainerStyles() {
    const position = this.getPositionStyles();
    
    return `
      position: fixed;
      ${position}
      width: ${this.options.size * 2}px;
      height: ${this.options.size * 2}px;
      pointer-events: none;
      z-index: ${this.options.zIndex};
      opacity: 0;
      transition: opacity 0.3s ease-in-out;
      will-change: transform, opacity;
    `;
  }

  /**
   * Get position styles based on position option
   */
  getPositionStyles() {
    const margin = 20;
    
    switch (this.options.position) {
      case 'top-left':
        return `top: ${margin}px; left: ${margin}px;`;
      case 'top-right':
        return `top: ${margin}px; right: ${margin}px;`;
      case 'bottom-left':
        return `bottom: ${margin}px; left: ${margin}px;`;
      case 'bottom-right':
        return `bottom: ${margin}px; right: ${margin}px;`;
      case 'center':
        return `top: 50%; left: 50%; transform: translate(-50%, -50%);`;
      default:
        return `bottom: ${margin}px; right: ${margin}px;`;
    }
  }

  /**
   * Get ball CSS styles
   */
  getBallStyles() {
    return `
      width: ${this.options.size}px;
      height: ${this.options.size}px;
      background: ${this.options.color};
      border-radius: 50%;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) scale(1);
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
      will-change: transform;
      transition: background-color 0.3s ease;
    `;
  }

  /**
   * Get glow CSS styles
   */
  getGlowStyles() {
    return `
      width: ${this.options.size * 1.5}px;
      height: ${this.options.size * 1.5}px;
      background: radial-gradient(circle, ${this.options.color}40 0%, transparent 70%);
      border-radius: 50%;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) scale(1);
      will-change: transform;
      opacity: 0.6;
    `;
  }

  /**
   * Create particle effects
   */
  createParticles() {
    const particleCount = 6;
    
    for (let i = 0; i < particleCount; i++) {
      const particle = document.createElement('div');
      particle.className = 'voice-agent-particle';
      particle.style.cssText = this.getParticleStyles(i, particleCount);
      
      this.particleElements.push(particle);
      this.container.appendChild(particle);
    }
  }

  /**
   * Get particle CSS styles
   */
  getParticleStyles(index, total) {
    const angle = (index / total) * 360;
    const size = 3;
    
    return `
      width: ${size}px;
      height: ${size}px;
      background: ${this.options.color};
      border-radius: 50%;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) rotate(${angle}deg) translateX(${this.options.size * 0.8}px);
      opacity: 0;
      will-change: transform, opacity;
    `;
  }

  /**
   * Show the pulsing ball
   */
  show() {
    if (this.state.isVisible) return;
    
    this.state.isVisible = true;
    this.container.style.opacity = '1';
    
    this.emit('shown');
    console.log('👁️ Pulsing ball shown');
  }

  /**
   * Hide the pulsing ball
   */
  hide() {
    if (!this.state.isVisible) return;
    
    this.state.isVisible = false;
    this.container.style.opacity = '0';
    
    // Stop any ongoing animations
    this.stopPulsing();
    
    this.emit('hidden');
    console.log('🙈 Pulsing ball hidden');
  }

  /**
   * Start pulsing animation
   */
  startPulsing(options = {}) {
    if (this.state.isPulsing) return;
    
    this.state.isPulsing = true;
    this.state.pulseStartTime = performance.now();
    
    const pulseOptions = {
      duration: this.animation.duration,
      intensity: this.options.intensity,
      ...options
    };
    
    this.animation.maxScale = 1 + pulseOptions.intensity;
    
    // Start animation loop
    this.startAnimationLoop();
    
    // Animate particles if enabled
    if (this.options.enableParticles) {
      this.animateParticles();
    }
    
    this.emit('pulseStart', pulseOptions);
    console.log('💓 Pulsing animation started');
  }

  /**
   * Stop pulsing animation
   */
  stopPulsing() {
    if (!this.state.isPulsing) return;
    
    this.state.isPulsing = false;
    
    // Cancel animation frame
    if (this.state.animationId) {
      cancelAnimationFrame(this.state.animationId);
      this.state.animationId = null;
    }
    
    // Reset to base scale
    this.setScale(this.animation.baseScale);
    
    // Stop particles
    if (this.options.enableParticles) {
      this.stopParticles();
    }
    
    this.emit('pulseStop');
    console.log('💤 Pulsing animation stopped');
  }

  /**
   * Start animation loop
   */
  startAnimationLoop() {
    const animate = (currentTime) => {
      if (!this.state.isPulsing) return;
      
      // Performance monitoring
      this.updatePerformanceMetrics(currentTime);
      
      // Calculate animation progress
      const elapsed = currentTime - this.state.pulseStartTime;
      const progress = (elapsed % this.animation.duration) / this.animation.duration;
      
      // Calculate scale based on waveform data or default sine wave
      let scale;
      if (this.animation.waveformData) {
        scale = this.calculateScaleFromWaveform(progress);
      } else {
        scale = this.calculateScaleFromSine(progress);
      }
      
      // Apply scale
      this.setScale(scale);
      
      // Continue animation
      this.state.animationId = requestAnimationFrame(animate);
    };
    
    this.state.animationId = requestAnimationFrame(animate);
  }

  /**
   * Calculate scale from sine wave (default animation)
   */
  calculateScaleFromSine(progress) {
    const sineValue = Math.sin(progress * Math.PI * 2);
    const normalizedValue = (sineValue + 1) / 2; // Normalize to 0-1
    
    return this.animation.baseScale + 
           (normalizedValue * (this.animation.maxScale - this.animation.baseScale));
  }

  /**
   * Calculate scale from waveform data (speech-synchronized)
   */
  calculateScaleFromWaveform(progress) {
    if (!this.animation.waveformData || this.animation.waveformData.length === 0) {
      return this.calculateScaleFromSine(progress);
    }
    
    const dataIndex = Math.floor(progress * this.animation.waveformData.length);
    const amplitude = this.animation.waveformData[dataIndex] || 0;
    
    return this.animation.baseScale + 
           (amplitude * (this.animation.maxScale - this.animation.baseScale));
  }

  /**
   * Set scale for ball and glow
   */
  setScale(scale) {
    this.state.currentScale = scale;
    
    const transform = this.options.position === 'center' 
      ? `translate(-50%, -50%) scale(${scale})`
      : `translate(-50%, -50%) scale(${scale})`;
    
    this.ballElement.style.transform = transform;
    
    if (this.glowElement) {
      const glowScale = scale * 0.8; // Slightly smaller glow scale
      this.glowElement.style.transform = `translate(-50%, -50%) scale(${glowScale})`;
    }
  }

  /**
   * Sync animation with audio waveform
   */
  syncWithWaveform(waveformData, duration) {
    this.animation.waveformData = waveformData;
    this.animation.duration = duration;
    
    console.log(`🎵 Syncing animation with ${waveformData.length} waveform points over ${duration}ms`);
    
    this.emit('waveformSynced', { waveformData, duration });
  }

  /**
   * Animate particles
   */
  animateParticles() {
    this.particleElements.forEach((particle, index) => {
      const delay = index * 100;
      
      setTimeout(() => {
        particle.style.opacity = '0.6';
        particle.style.transform += ' scale(1.2)';
      }, delay);
    });
  }

  /**
   * Stop particle animations
   */
  stopParticles() {
    this.particleElements.forEach(particle => {
      particle.style.opacity = '0';
      particle.style.transform = particle.style.transform.replace(' scale(1.2)', '');
    });
  }

  /**
   * Update settings
   */
  updateSettings(settings) {
    const oldSettings = { ...this.options };
    this.options = { ...this.options, ...settings };
    
    // Update visual properties
    if (settings.color && settings.color !== oldSettings.color) {
      this.ballElement.style.background = settings.color;
      if (this.glowElement) {
        this.glowElement.style.background = 
          `radial-gradient(circle, ${settings.color}40 0%, transparent 70%)`;
      }
    }
    
    if (settings.size && settings.size !== oldSettings.size) {
      this.ballElement.style.width = `${settings.size}px`;
      this.ballElement.style.height = `${settings.size}px`;
    }
    
    if (settings.intensity && settings.intensity !== oldSettings.intensity) {
      this.animation.maxScale = 1 + settings.intensity;
    }
    
    this.emit('settingsUpdated', settings);
    console.log('⚙️ Pulsing ball settings updated:', settings);
  }

  /**
   * Update performance metrics
   */
  updatePerformanceMetrics(currentTime) {
    if (this.performance.lastFrameTime > 0) {
      const deltaTime = currentTime - this.performance.lastFrameTime;
      const fps = 1000 / deltaTime;
      
      this.performance.frameCount++;
      this.performance.averageFPS = 
        (this.performance.averageFPS * 0.9) + (fps * 0.1);
      
      if (fps < 50) {
        this.performance.droppedFrames++;
      }
    }
    
    this.performance.lastFrameTime = currentTime;
  }

  /**
   * Check if user prefers reduced motion
   */
  prefersReducedMotion() {
    return window.matchMedia && 
           window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // Listen for motion preference changes
    if (window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
      mediaQuery.addEventListener('change', (e) => {
        if (e.matches) {
          this.updateSettings({ intensity: 0.1 });
        }
      });
    }
    
    // Listen for visibility changes
    document.addEventListener('visibilitychange', () => {
      if (document.hidden && this.state.isPulsing) {
        this.stopPulsing();
      }
    });
  }

  /**
   * Event system
   */
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(callback);
  }

  emit(event, data) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Error in pulsing ball event listener:', error);
        }
      });
    }
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics() {
    return {
      ...this.performance,
      isAnimating: this.state.isPulsing,
      currentScale: this.state.currentScale,
      droppedFrameRate: this.performance.droppedFrames / this.performance.frameCount
    };
  }

  /**
   * Get current state
   */
  getState() {
    return {
      ...this.state,
      settings: this.options,
      performance: this.getPerformanceMetrics()
    };
  }

  /**
   * Cleanup and destroy
   */
  destroy() {
    this.stopPulsing();
    
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
    }
    
    this.eventListeners.clear();
    
    console.log('🗑️ Pulsing ball destroyed');
  }
}

export default PulsingBall;
