/**
 * VoiceAgent - Main AI Voice Agent Component
 * Handles voice synthesis, conversation flow, and user interactions
 */

import { SpeechService } from '../services/speechService.js';
import { AnimationService } from '../services/animationService.js';
import { ClientHistoryService } from '../services/clientHistory.js';
import { AILogicService } from '../services/aiLogic.js';
import { ConversationFlow } from './ConversationFlow.js';
import { PulsingBall } from './PulsingBall.js';

export class VoiceAgent {
  constructor(options = {}) {
    this.options = {
      autoStart: true,
      enableAnimation: true,
      enableVoice: true,
      debugMode: false,
      ...options
    };

    // Core services
    this.speechService = new SpeechService();
    this.animationService = new AnimationService();
    this.clientHistoryService = new ClientHistoryService();
    this.aiLogicService = new AILogicService();
    
    // Components
    this.conversationFlow = new ConversationFlow(this);
    this.pulsingBall = null;
    
    // State management
    this.state = {
      isActive: false,
      isListening: false,
      isSpeaking: false,
      currentUser: null,
      conversationId: null,
      sessionId: this.generateSessionId(),
      lastInteraction: null
    };

    // Event listeners
    this.eventListeners = new Map();
    
    // Initialize
    this.init();
  }

  /**
   * Initialize the voice agent
   */
  async init() {
    try {
      this.log('Initializing AI Voice Agent...');
      
      // Initialize services
      await this.speechService.init();
      await this.animationService.init();
      
      // Create pulsing ball if animation is enabled
      if (this.options.enableAnimation) {
        this.pulsingBall = new PulsingBall({
          container: document.body,
          speechService: this.speechService
        });
      }
      
      // Set up event listeners
      this.setupEventListeners();
      
      this.log('AI Voice Agent initialized successfully');
      this.emit('initialized');
      
    } catch (error) {
      this.error('Failed to initialize AI Voice Agent:', error);
      this.emit('error', error);
    }
  }

  /**
   * Activate voice agent for authenticated user
   */
  async activate(user) {
    try {
      this.log('Activating voice agent for user:', user.email);
      
      this.state.currentUser = user;
      this.state.isActive = true;
      this.state.conversationId = this.generateConversationId();
      
      // Load client history
      const clientHistory = await this.clientHistoryService.getClientHistory(user.id);
      
      // Generate personalized greeting
      const greeting = await this.aiLogicService.generateGreeting(user, clientHistory);
      
      // Start conversation
      await this.conversationFlow.start(greeting, clientHistory);
      
      // Show pulsing ball
      if (this.pulsingBall) {
        this.pulsingBall.show();
      }
      
      this.emit('activated', { user, greeting });
      
    } catch (error) {
      this.error('Failed to activate voice agent:', error);
      this.emit('error', error);
    }
  }

  /**
   * Deactivate voice agent
   */
  async deactivate() {
    try {
      this.log('Deactivating voice agent...');
      
      // Stop any ongoing speech
      await this.speechService.stop();
      
      // Hide pulsing ball
      if (this.pulsingBall) {
        this.pulsingBall.hide();
      }
      
      // End conversation
      if (this.conversationFlow.isActive()) {
        await this.conversationFlow.end();
      }
      
      // Reset state
      this.state.isActive = false;
      this.state.currentUser = null;
      this.state.conversationId = null;
      
      this.emit('deactivated');
      
    } catch (error) {
      this.error('Failed to deactivate voice agent:', error);
    }
  }

  /**
   * Speak a message with animation synchronization
   */
  async speak(message, options = {}) {
    if (!this.options.enableVoice || !this.state.isActive) {
      return;
    }

    try {
      this.state.isSpeaking = true;
      this.emit('speechStart', { message });
      
      // Start pulsing animation
      if (this.pulsingBall) {
        this.pulsingBall.startPulsing();
      }
      
      // Synthesize and play speech
      const audioData = await this.speechService.synthesize(message, {
        voice: this.state.currentUser?.voicePreferences?.voice,
        speed: this.state.currentUser?.voicePreferences?.speed,
        pitch: this.state.currentUser?.voicePreferences?.pitch,
        volume: this.state.currentUser?.voicePreferences?.volume,
        ...options
      });
      
      // Sync animation with audio
      if (this.pulsingBall && audioData.duration) {
        this.animationService.syncWithAudio(
          this.pulsingBall,
          audioData.duration,
          audioData.waveform
        );
      }
      
      // Play audio
      await this.speechService.play(audioData);
      
      this.emit('speechEnd', { message, duration: audioData.duration });
      
    } catch (error) {
      this.error('Failed to speak message:', error);
      this.emit('speechError', error);
    } finally {
      this.state.isSpeaking = false;
      
      // Stop pulsing animation
      if (this.pulsingBall) {
        this.pulsingBall.stopPulsing();
      }
    }
  }

  /**
   * Process user input (text or voice)
   */
  async processInput(input, type = 'text') {
    if (!this.state.isActive) {
      return;
    }

    try {
      this.log('Processing user input:', input);
      
      // Update last interaction
      this.state.lastInteraction = new Date();
      
      // Process input through AI logic
      const response = await this.aiLogicService.processInput(
        input,
        type,
        this.state.currentUser,
        this.conversationFlow.getContext()
      );
      
      // Handle response through conversation flow
      await this.conversationFlow.handleResponse(response);
      
      this.emit('inputProcessed', { input, type, response });
      
    } catch (error) {
      this.error('Failed to process input:', error);
      this.emit('inputError', error);
    }
  }

  /**
   * Handle appointment booking
   */
  async bookAppointment(appointmentData) {
    try {
      this.log('Booking appointment:', appointmentData);
      
      const booking = await this.clientHistoryService.bookAppointment(
        this.state.currentUser.id,
        appointmentData
      );
      
      const confirmationMessage = await this.aiLogicService.generateBookingConfirmation(
        booking,
        this.state.currentUser
      );
      
      await this.speak(confirmationMessage);
      
      this.emit('appointmentBooked', booking);
      
      return booking;
      
    } catch (error) {
      this.error('Failed to book appointment:', error);
      
      const errorMessage = await this.aiLogicService.generateBookingError(
        error,
        this.state.currentUser
      );
      
      await this.speak(errorMessage);
      
      this.emit('bookingError', error);
      throw error;
    }
  }

  /**
   * Update user preferences
   */
  async updatePreferences(preferences) {
    try {
      this.log('Updating user preferences:', preferences);
      
      // Update voice preferences
      if (preferences.voice) {
        await this.speechService.updateVoiceSettings(preferences.voice);
      }
      
      // Update animation preferences
      if (preferences.animation && this.pulsingBall) {
        this.pulsingBall.updateSettings(preferences.animation);
      }
      
      // Save to backend
      await this.clientHistoryService.updatePreferences(
        this.state.currentUser.id,
        preferences
      );
      
      this.emit('preferencesUpdated', preferences);
      
    } catch (error) {
      this.error('Failed to update preferences:', error);
      this.emit('preferencesError', error);
    }
  }

  /**
   * Get current conversation context
   */
  getContext() {
    return {
      user: this.state.currentUser,
      conversationId: this.state.conversationId,
      sessionId: this.state.sessionId,
      isActive: this.state.isActive,
      isSpeaking: this.state.isSpeaking,
      lastInteraction: this.state.lastInteraction,
      conversationFlow: this.conversationFlow.getContext()
    };
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // Listen for authentication events
    window.addEventListener('userAuthenticated', (event) => {
      if (this.options.autoStart) {
        this.activate(event.detail.user);
      }
    });
    
    // Listen for user logout
    window.addEventListener('userLoggedOut', () => {
      this.deactivate();
    });
    
    // Listen for page visibility changes
    document.addEventListener('visibilitychange', () => {
      if (document.hidden && this.state.isSpeaking) {
        this.speechService.pause();
      } else if (!document.hidden && this.state.isSpeaking) {
        this.speechService.resume();
      }
    });
    
    // Listen for window focus/blur
    window.addEventListener('blur', () => {
      if (this.state.isSpeaking) {
        this.speechService.pause();
      }
    });
    
    window.addEventListener('focus', () => {
      if (this.state.isSpeaking) {
        this.speechService.resume();
      }
    });
  }

  /**
   * Event system
   */
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(callback);
  }

  off(event, callback) {
    if (this.eventListeners.has(event)) {
      const listeners = this.eventListeners.get(event);
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  emit(event, data) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          this.error('Error in event listener:', error);
        }
      });
    }
  }

  /**
   * Utility methods
   */
  generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  generateConversationId() {
    return `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  log(...args) {
    if (this.options.debugMode) {
      console.log('[VoiceAgent]', ...args);
    }
  }

  error(...args) {
    console.error('[VoiceAgent]', ...args);
  }

  /**
   * Cleanup
   */
  destroy() {
    this.deactivate();
    
    if (this.pulsingBall) {
      this.pulsingBall.destroy();
    }
    
    this.speechService.destroy();
    this.animationService.destroy();
    
    this.eventListeners.clear();
  }
}

// Export for use in other modules
export default VoiceAgent;
