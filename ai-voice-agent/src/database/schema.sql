-- AI Voice Agent Database Schema
-- This schema extends the existing Barber Brothers Legacy database
-- Compatible with MongoDB (NoSQL) structure but shown in SQL for clarity

-- ============================================================================
-- CLIENT PROFILES TABLE
-- ============================================================================
-- Stores client information and preferences for personalized voice interactions
CREATE TABLE IF NOT EXISTS clients (
    id VARCHAR(24) PRIMARY KEY,                    -- MongoDB ObjectId
    email VARCHAR(255) UNIQUE NOT NULL,            -- Google OAuth email
    google_id VARCHAR(255) UNIQUE,                 -- Google OAuth ID
    first_name VARCHAR(100) NOT NULL,              -- First name from Google
    last_name VARCHAR(100),                        -- Last name from Google
    display_name VARCHAR(200),                     -- Preferred display name
    phone VARCHAR(20),                             -- Phone number (optional)
    
    -- Registration and Activity
    registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP,
    total_visits INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    
    -- Voice Agent Preferences
    voice_enabled BOOLEAN DEFAULT TRUE,            -- User preference for voice agent
    preferred_voice VARCHAR(50) DEFAULT 'en-US-Standard-A', -- TTS voice preference
    voice_speed DECIMAL(3,2) DEFAULT 1.0,         -- Speech rate (0.5-2.0)
    voice_pitch DECIMAL(3,2) DEFAULT 1.0,         -- Speech pitch (0.5-2.0)
    voice_volume DECIMAL(3,2) DEFAULT 0.8,        -- Speech volume (0.0-1.0)
    
    -- Animation Preferences
    animation_enabled BOOLEAN DEFAULT TRUE,        -- Pulsing ball animation
    animation_intensity DECIMAL(3,2) DEFAULT 0.3, -- Pulse intensity (0.1-1.0)
    animation_color VARCHAR(7) DEFAULT '#ff6b6b', -- Hex color for pulsing ball
    reduce_motion BOOLEAN DEFAULT FALSE,           -- Accessibility preference
    
    -- Personalization Data
    preferred_greeting VARCHAR(500),               -- Custom greeting message
    communication_style ENUM('formal', 'casual', 'friendly') DEFAULT 'friendly',
    language_preference VARCHAR(10) DEFAULT 'en-US',
    timezone VARCHAR(50) DEFAULT 'America/New_York',
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_email (email),
    INDEX idx_google_id (google_id),
    INDEX idx_last_login (last_login_at),
    INDEX idx_active (is_active)
);

-- ============================================================================
-- APPOINTMENTS TABLE
-- ============================================================================
-- Stores appointment history for contextual conversations
CREATE TABLE IF NOT EXISTS appointments (
    id VARCHAR(24) PRIMARY KEY,                    -- MongoDB ObjectId
    client_id VARCHAR(24) NOT NULL,                -- Reference to clients table
    
    -- Appointment Details
    service_type VARCHAR(100) NOT NULL,            -- Type of service (haircut, beard trim, etc.)
    service_category ENUM('haircut', 'beard', 'styling', 'consultation', 'other') DEFAULT 'haircut',
    appointment_date TIMESTAMP NOT NULL,           -- Date and time of appointment
    duration_minutes INTEGER DEFAULT 60,           -- Duration in minutes
    status ENUM('scheduled', 'completed', 'cancelled', 'no_show') DEFAULT 'scheduled',
    
    -- Service Details
    stylist_name VARCHAR(100) DEFAULT 'Andre The Barber',
    price DECIMAL(10,2),                          -- Service price
    tip_amount DECIMAL(10,2) DEFAULT 0.00,       -- Tip amount
    total_amount DECIMAL(10,2),                   -- Total paid
    
    -- Service Specifics
    haircut_style VARCHAR(200),                   -- Specific style requested
    hair_length_before VARCHAR(50),              -- Hair length before cut
    hair_length_after VARCHAR(50),               -- Hair length after cut
    special_requests TEXT,                        -- Special instructions
    products_used JSON,                           -- Products used during service
    
    -- Follow-up Information
    next_suggested_date DATE,                     -- AI-suggested next appointment
    client_satisfaction INTEGER CHECK (client_satisfaction BETWEEN 1 AND 5), -- 1-5 rating
    notes TEXT,                                   -- Stylist notes
    before_photo_url VARCHAR(500),               -- Before photo URL
    after_photo_url VARCHAR(500),                -- After photo URL
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    INDEX idx_client_id (client_id),
    INDEX idx_appointment_date (appointment_date),
    INDEX idx_service_type (service_type),
    INDEX idx_status (status),
    INDEX idx_next_suggested (next_suggested_date)
);

-- ============================================================================
-- CONVERSATIONS TABLE
-- ============================================================================
-- Logs voice agent conversations for improvement and personalization
CREATE TABLE IF NOT EXISTS conversations (
    id VARCHAR(24) PRIMARY KEY,                    -- MongoDB ObjectId
    client_id VARCHAR(24) NOT NULL,                -- Reference to clients table
    session_id VARCHAR(100) NOT NULL,              -- Unique session identifier
    
    -- Conversation Details
    conversation_type ENUM('welcome', 'booking', 'inquiry', 'follow_up', 'general') DEFAULT 'welcome',
    trigger_event ENUM('login', 'manual', 'scheduled', 'callback') DEFAULT 'login',
    conversation_start TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    conversation_end TIMESTAMP,
    duration_seconds INTEGER,
    
    -- Conversation Content
    messages JSON,                                 -- Array of conversation messages
    conversation_summary TEXT,                     -- AI-generated summary
    client_intent VARCHAR(200),                   -- Detected client intent
    agent_responses JSON,                         -- Agent response history
    
    -- Interaction Metrics
    user_interruptions INTEGER DEFAULT 0,         -- Times user interrupted agent
    agent_restarts INTEGER DEFAULT 0,             -- Times agent was restarted
    successful_completion BOOLEAN DEFAULT FALSE,   -- Whether conversation completed successfully
    user_satisfaction INTEGER CHECK (user_satisfaction BETWEEN 1 AND 5), -- User feedback
    
    -- Outcomes
    appointment_booked BOOLEAN DEFAULT FALSE,      -- Whether appointment was booked
    appointment_id VARCHAR(24),                   -- Reference to booked appointment
    follow_up_required BOOLEAN DEFAULT FALSE,     -- Whether follow-up is needed
    follow_up_date TIMESTAMP,                     -- When to follow up
    
    -- Technical Details
    voice_synthesis_time_ms INTEGER,              -- Time to generate speech
    animation_sync_quality DECIMAL(3,2),         -- Animation sync quality (0.0-1.0)
    errors_encountered JSON,                      -- Any errors during conversation
    
    -- Preferences Updated
    preferences_updated JSON,                     -- Any preference changes made
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    FOREIGN KEY (appointment_id) REFERENCES appointments(id) ON DELETE SET NULL,
    INDEX idx_client_id (client_id),
    INDEX idx_session_id (session_id),
    INDEX idx_conversation_start (conversation_start),
    INDEX idx_conversation_type (conversation_type),
    INDEX idx_successful_completion (successful_completion)
);

-- ============================================================================
-- VOICE_AGENT_ANALYTICS TABLE
-- ============================================================================
-- Tracks voice agent performance and usage analytics
CREATE TABLE IF NOT EXISTS voice_agent_analytics (
    id VARCHAR(24) PRIMARY KEY,                    -- MongoDB ObjectId
    
    -- Time Period
    date_recorded DATE NOT NULL,                   -- Date of analytics
    hour_of_day INTEGER CHECK (hour_of_day BETWEEN 0 AND 23), -- Hour (0-23)
    
    -- Usage Metrics
    total_activations INTEGER DEFAULT 0,          -- Total voice agent activations
    successful_conversations INTEGER DEFAULT 0,    -- Successful conversations
    failed_conversations INTEGER DEFAULT 0,       -- Failed conversations
    average_conversation_duration DECIMAL(8,2),   -- Average duration in seconds
    
    -- Performance Metrics
    average_response_time_ms INTEGER,             -- Average response time
    average_synthesis_time_ms INTEGER,            -- Average speech synthesis time
    animation_sync_success_rate DECIMAL(5,2),    -- Animation sync success rate
    
    -- User Engagement
    new_users INTEGER DEFAULT 0,                 -- New users who used voice agent
    returning_users INTEGER DEFAULT 0,           -- Returning users
    user_satisfaction_average DECIMAL(3,2),      -- Average satisfaction rating
    
    -- Conversion Metrics
    appointments_booked INTEGER DEFAULT 0,        -- Appointments booked via voice agent
    conversion_rate DECIMAL(5,2),                -- Booking conversion rate
    
    -- Technical Metrics
    error_rate DECIMAL(5,2),                     -- Error rate percentage
    uptime_percentage DECIMAL(5,2),              -- Service uptime
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_date_hour (date_recorded, hour_of_day),
    INDEX idx_date_recorded (date_recorded),
    INDEX idx_hour_of_day (hour_of_day)
);

-- ============================================================================
-- VOICE_AGENT_SETTINGS TABLE
-- ============================================================================
-- Global settings for the voice agent system
CREATE TABLE IF NOT EXISTS voice_agent_settings (
    id VARCHAR(24) PRIMARY KEY,                    -- MongoDB ObjectId
    setting_key VARCHAR(100) UNIQUE NOT NULL,      -- Setting identifier
    setting_value TEXT,                           -- Setting value (JSON or string)
    setting_type ENUM('string', 'number', 'boolean', 'json', 'array') DEFAULT 'string',
    description TEXT,                             -- Setting description
    is_active BOOLEAN DEFAULT TRUE,               -- Whether setting is active
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_setting_key (setting_key),
    INDEX idx_is_active (is_active)
);

-- ============================================================================
-- INITIAL DATA INSERTS
-- ============================================================================

-- Default voice agent settings
INSERT INTO voice_agent_settings (setting_key, setting_value, setting_type, description) VALUES
('default_greeting_new_user', 'Welcome to Barber Brothers Legacy! I''m your AI assistant. I see this is your first visit with us.', 'string', 'Default greeting for new users'),
('default_greeting_returning_user', 'Welcome back to Barber Brothers Legacy! Great to see you again.', 'string', 'Default greeting for returning users'),
('voice_agent_enabled', 'true', 'boolean', 'Global enable/disable for voice agent'),
('animation_enabled', 'true', 'boolean', 'Global enable/disable for pulsing ball animation'),
('max_conversation_duration', '300', 'number', 'Maximum conversation duration in seconds'),
('default_voice_settings', '{"voice": "en-US-Standard-A", "speed": 1.0, "pitch": 1.0, "volume": 0.8}', 'json', 'Default voice synthesis settings'),
('business_hours', '{"monday": {"open": "09:00", "close": "18:00"}, "tuesday": {"open": "09:00", "close": "18:00"}, "wednesday": {"open": "09:00", "close": "18:00"}, "thursday": {"open": "09:00", "close": "18:00"}, "friday": {"open": "09:00", "close": "18:00"}, "saturday": {"open": "08:00", "close": "16:00"}, "sunday": {"closed": true}}', 'json', 'Business hours for appointment scheduling'),
('service_types', '["Classic Haircut", "Beard Trim", "Hot Towel Shave", "Hair Styling", "Consultation"]', 'array', 'Available service types'),
('animation_colors', '["#ff6b6b", "#4ecdc4", "#45b7d1", "#96ceb4", "#feca57"]', 'array', 'Available animation colors');
