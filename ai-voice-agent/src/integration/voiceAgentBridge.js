/**
 * Voice Agent Bridge - Integration Module
 * Connects the AI Voice Agent with the existing Barber Brothers authentication system
 */

import { VoiceAgent } from '../components/VoiceAgent.js';

export class VoiceAgentBridge {
  constructor(options = {}) {
    this.options = {
      autoActivateOnLogin: true,
      enableDebugMode: false,
      apiBaseUrl: '/api',
      ...options
    };

    this.voiceAgent = null;
    this.isInitialized = false;
    this.currentUser = null;
    
    // Event listeners
    this.eventListeners = new Map();
    
    this.init();
  }

  /**
   * Initialize the bridge
   */
  async init() {
    try {
      console.log('🌉 Initializing Voice Agent Bridge...');
      
      // Initialize voice agent
      this.voiceAgent = new VoiceAgent({
        autoStart: false, // We'll control activation manually
        enableAnimation: true,
        enableVoice: true,
        debugMode: this.options.enableDebugMode
      });

      // Set up event listeners
      this.setupEventListeners();
      
      // Check for existing authentication
      await this.checkExistingAuth();
      
      this.isInitialized = true;
      console.log('✅ Voice Agent Bridge initialized successfully');
      
    } catch (error) {
      console.error('❌ Failed to initialize Voice Agent Bridge:', error);
      throw error;
    }
  }

  /**
   * Set up event listeners for authentication events
   */
  setupEventListeners() {
    // Listen for localStorage changes (auth events)
    window.addEventListener('storage', (event) => {
      if (event.key === 'authSuccess' && event.newValue === 'true') {
        this.handleAuthSuccess();
      } else if (event.key === 'userProfile' && event.newValue) {
        this.handleUserProfileUpdate(JSON.parse(event.newValue));
      }
    });

    // Listen for custom auth events
    window.addEventListener('googleAuthSuccess', (event) => {
      this.handleAuthSuccess(event.detail);
    });

    // Listen for logout events
    window.addEventListener('userLogout', () => {
      this.handleLogout();
    });

    // Voice agent events
    if (this.voiceAgent) {
      this.voiceAgent.on('activated', (data) => {
        console.log('🎤 Voice agent activated for user:', data.user.email);
        this.emit('voiceAgentActivated', data);
      });

      this.voiceAgent.on('deactivated', () => {
        console.log('🔇 Voice agent deactivated');
        this.emit('voiceAgentDeactivated');
      });

      this.voiceAgent.on('error', (error) => {
        console.error('🚨 Voice agent error:', error);
        this.emit('voiceAgentError', error);
      });
    }
  }

  /**
   * Check for existing authentication on page load
   */
  async checkExistingAuth() {
    try {
      // Check localStorage for existing auth
      const authSuccess = localStorage.getItem('authSuccess');
      const userProfile = localStorage.getItem('userProfile');
      
      if (authSuccess === 'true' && userProfile) {
        const user = JSON.parse(userProfile);
        console.log('🔍 Found existing authentication for:', user.email);
        
        if (this.options.autoActivateOnLogin) {
          await this.activateVoiceAgent(user);
        }
      }
      
    } catch (error) {
      console.warn('⚠️ Error checking existing auth:', error);
    }
  }

  /**
   * Handle successful authentication
   */
  async handleAuthSuccess(userData = null) {
    try {
      console.log('🎉 Authentication success detected');
      
      // Get user data from localStorage if not provided
      let user = userData;
      if (!user) {
        const userProfile = localStorage.getItem('userProfile');
        if (userProfile) {
          user = JSON.parse(userProfile);
        }
      }

      if (!user) {
        console.warn('⚠️ No user data available for voice agent activation');
        return;
      }

      // Store current user
      this.currentUser = user;

      // Activate voice agent if enabled
      if (this.options.autoActivateOnLogin) {
        // Add a small delay to ensure page is ready
        setTimeout(() => {
          this.activateVoiceAgent(user);
        }, 1000);
      }

    } catch (error) {
      console.error('❌ Error handling auth success:', error);
    }
  }

  /**
   * Handle user profile updates
   */
  handleUserProfileUpdate(user) {
    console.log('👤 User profile updated:', user.email);
    this.currentUser = user;
    
    // Update voice agent if active
    if (this.voiceAgent && this.voiceAgent.isActive()) {
      this.voiceAgent.updateUser(user);
    }
  }

  /**
   * Handle user logout
   */
  handleLogout() {
    console.log('👋 User logout detected');
    
    this.currentUser = null;
    
    // Deactivate voice agent
    if (this.voiceAgent && this.voiceAgent.isActive()) {
      this.voiceAgent.deactivate();
    }
    
    // Clear localStorage
    localStorage.removeItem('authSuccess');
    localStorage.removeItem('userProfile');
    localStorage.removeItem('googleAuthData');
  }

  /**
   * Activate voice agent for user
   */
  async activateVoiceAgent(user) {
    if (!this.voiceAgent) {
      console.error('❌ Voice agent not initialized');
      return;
    }

    try {
      console.log('🚀 Activating voice agent for user:', user.email);
      
      // Transform user data to match voice agent format
      const voiceAgentUser = this.transformUserData(user);
      
      // Activate voice agent
      await this.voiceAgent.activate(voiceAgentUser);
      
      console.log('✅ Voice agent activated successfully');
      
    } catch (error) {
      console.error('❌ Failed to activate voice agent:', error);
      this.emit('activationError', error);
    }
  }

  /**
   * Transform user data from auth system to voice agent format
   */
  transformUserData(user) {
    return {
      id: user.sub || user.id || user.email, // Google ID or fallback
      email: user.email,
      name: user.name || user.given_name || 'Valued Client',
      firstName: user.given_name || user.name?.split(' ')[0] || 'Valued Client',
      lastName: user.family_name || user.name?.split(' ').slice(1).join(' ') || '',
      picture: user.picture || user.avatar || null,
      verified: user.email_verified || false,
      loginTime: new Date(),
      voicePreferences: {
        voice: 'en-US-Standard-A',
        speed: 1.0,
        pitch: 1.0,
        volume: 0.8
      }
    };
  }

  /**
   * Manually activate voice agent
   */
  async activate() {
    if (!this.currentUser) {
      console.warn('⚠️ No authenticated user available');
      return false;
    }

    return await this.activateVoiceAgent(this.currentUser);
  }

  /**
   * Manually deactivate voice agent
   */
  deactivate() {
    if (this.voiceAgent && this.voiceAgent.isActive()) {
      this.voiceAgent.deactivate();
    }
  }

  /**
   * Get current user
   */
  getCurrentUser() {
    return this.currentUser;
  }

  /**
   * Check if voice agent is active
   */
  isActive() {
    return this.voiceAgent && this.voiceAgent.isActive();
  }

  /**
   * Event emitter methods
   */
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(callback);
  }

  emit(event, data) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in event listener for ${event}:`, error);
        }
      });
    }
  }

  /**
   * Cleanup
   */
  destroy() {
    if (this.voiceAgent) {
      this.voiceAgent.destroy();
    }
    
    this.eventListeners.clear();
    this.currentUser = null;
    this.isInitialized = false;
  }
}

// Global instance for easy access
window.VoiceAgentBridge = VoiceAgentBridge;
