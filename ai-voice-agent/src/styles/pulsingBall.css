/**
 * Pulsing Ball Animation Styles
 * Optimized for performance and accessibility
 */

/* Container for the pulsing ball */
.voice-agent-pulsing-ball-container {
  /* Position and layout handled by JavaScript */
  contain: layout style paint;
  isolation: isolate;
}

/* Main pulsing ball element */
.voice-agent-pulsing-ball {
  /* Base styles handled by JavaScript */
  backface-visibility: hidden;
  transform-origin: center center;
  
  /* Smooth transitions for color changes */
  transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Hardware acceleration */
  will-change: transform;
  transform: translateZ(0);
  
  /* Subtle shadow for depth */
  box-shadow: 
    0 2px 8px rgba(0, 0, 0, 0.15),
    0 1px 3px rgba(0, 0, 0, 0.2);
  
  /* Prevent text selection */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Glow effect element */
.voice-agent-pulsing-ball-glow {
  backface-visibility: hidden;
  transform-origin: center center;
  will-change: transform;
  transform: translateZ(0);
  
  /* Smooth opacity transitions */
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Blend mode for better glow effect */
  mix-blend-mode: screen;
  
  /* Prevent pointer events */
  pointer-events: none;
}

/* Particle elements */
.voice-agent-particle {
  backface-visibility: hidden;
  transform-origin: center center;
  will-change: transform, opacity;
  transform: translateZ(0);
  
  /* Smooth transitions */
  transition: 
    opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1),
    transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Prevent pointer events */
  pointer-events: none;
}

/* Animation states */
.voice-agent-pulsing-ball-container.pulsing .voice-agent-pulsing-ball {
  animation: none; /* Controlled by JavaScript for better sync */
}

.voice-agent-pulsing-ball-container.pulsing .voice-agent-particle {
  animation: particle-float 2s ease-in-out infinite;
}

/* Particle floating animation */
@keyframes particle-float {
  0%, 100% {
    transform: translate(-50%, -50%) rotate(var(--particle-angle)) 
               translateX(var(--particle-distance)) scale(0.8);
    opacity: 0.3;
  }
  50% {
    transform: translate(-50%, -50%) rotate(var(--particle-angle)) 
               translateX(calc(var(--particle-distance) * 1.2)) scale(1.2);
    opacity: 0.8;
  }
}

/* Accessibility: Respect reduced motion preference */
@media (prefers-reduced-motion: reduce) {
  .voice-agent-pulsing-ball-container,
  .voice-agent-pulsing-ball,
  .voice-agent-pulsing-ball-glow,
  .voice-agent-particle {
    animation: none !important;
    transition: none !important;
  }
  
  .voice-agent-pulsing-ball-container.pulsing .voice-agent-pulsing-ball {
    transform: translate(-50%, -50%) scale(1.05) !important;
  }
  
  .voice-agent-pulsing-ball-container.pulsing .voice-agent-particle {
    opacity: 0.5 !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .voice-agent-pulsing-ball {
    border: 2px solid currentColor;
    box-shadow: 
      0 0 0 1px rgba(255, 255, 255, 0.5),
      0 2px 8px rgba(0, 0, 0, 0.3);
  }
  
  .voice-agent-pulsing-ball-glow {
    opacity: 0.3 !important;
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .voice-agent-pulsing-ball {
    box-shadow: 
      0 2px 8px rgba(0, 0, 0, 0.4),
      0 1px 3px rgba(0, 0, 0, 0.6),
      0 0 0 1px rgba(255, 255, 255, 0.1);
  }
}

/* Performance optimizations for different screen sizes */
@media (max-width: 768px) {
  .voice-agent-pulsing-ball-container {
    /* Reduce complexity on mobile */
    will-change: opacity;
  }
  
  .voice-agent-pulsing-ball-glow {
    /* Disable glow on mobile for better performance */
    display: none;
  }
  
  .voice-agent-particle {
    /* Disable particles on mobile */
    display: none;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .voice-agent-pulsing-ball {
    /* Sharper shadows on high DPI */
    box-shadow: 
      0 1px 4px rgba(0, 0, 0, 0.15),
      0 0.5px 1.5px rgba(0, 0, 0, 0.2);
  }
}

/* Focus styles for accessibility (if interactive) */
.voice-agent-pulsing-ball-container:focus-within {
  outline: 2px solid #4A90E2;
  outline-offset: 4px;
  border-radius: 50%;
}

/* Color theme variations */
.voice-agent-pulsing-ball-container.theme-red .voice-agent-pulsing-ball {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
}

.voice-agent-pulsing-ball-container.theme-blue .voice-agent-pulsing-ball {
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
}

.voice-agent-pulsing-ball-container.theme-green .voice-agent-pulsing-ball {
  background: linear-gradient(135deg, #96ceb4, #85c1a3);
}

.voice-agent-pulsing-ball-container.theme-purple .voice-agent-pulsing-ball {
  background: linear-gradient(135deg, #a8e6cf, #88d8a3);
}

.voice-agent-pulsing-ball-container.theme-orange .voice-agent-pulsing-ball {
  background: linear-gradient(135deg, #feca57, #ff9ff3);
}

/* Size variations */
.voice-agent-pulsing-ball-container.size-small .voice-agent-pulsing-ball {
  width: 20px;
  height: 20px;
}

.voice-agent-pulsing-ball-container.size-medium .voice-agent-pulsing-ball {
  width: 30px;
  height: 30px;
}

.voice-agent-pulsing-ball-container.size-large .voice-agent-pulsing-ball {
  width: 40px;
  height: 40px;
}

/* Intensity variations */
.voice-agent-pulsing-ball-container.intensity-low {
  --max-scale: 1.1;
}

.voice-agent-pulsing-ball-container.intensity-medium {
  --max-scale: 1.3;
}

.voice-agent-pulsing-ball-container.intensity-high {
  --max-scale: 1.5;
}

/* Position-specific adjustments */
.voice-agent-pulsing-ball-container.position-center {
  /* Center position might need different shadows */
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

.voice-agent-pulsing-ball-container.position-corner {
  /* Corner positions might need subtle borders */
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

/* Animation performance classes */
.voice-agent-pulsing-ball-container.performance-mode {
  /* Simplified animations for better performance */
  animation-duration: 1s;
  animation-timing-function: linear;
}

.voice-agent-pulsing-ball-container.performance-mode .voice-agent-pulsing-ball-glow,
.voice-agent-pulsing-ball-container.performance-mode .voice-agent-particle {
  display: none;
}

/* Error state */
.voice-agent-pulsing-ball-container.error .voice-agent-pulsing-ball {
  background: linear-gradient(135deg, #ff4757, #ff3838);
  animation: error-pulse 0.5s ease-in-out 3;
}

@keyframes error-pulse {
  0%, 100% { transform: translate(-50%, -50%) scale(1); }
  50% { transform: translate(-50%, -50%) scale(1.2); }
}

/* Success state */
.voice-agent-pulsing-ball-container.success .voice-agent-pulsing-ball {
  background: linear-gradient(135deg, #2ed573, #1e90ff);
  animation: success-pulse 0.3s ease-in-out 2;
}

@keyframes success-pulse {
  0%, 100% { transform: translate(-50%, -50%) scale(1); }
  50% { transform: translate(-50%, -50%) scale(1.15); }
}

/* Loading state */
.voice-agent-pulsing-ball-container.loading .voice-agent-pulsing-ball {
  background: linear-gradient(135deg, #ffa502, #ff6348);
  animation: loading-spin 1s linear infinite;
}

@keyframes loading-spin {
  from { transform: translate(-50%, -50%) rotate(0deg); }
  to { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Utility classes */
.voice-agent-hidden {
  opacity: 0 !important;
  pointer-events: none !important;
}

.voice-agent-visible {
  opacity: 1 !important;
}

/* Print styles */
@media print {
  .voice-agent-pulsing-ball-container {
    display: none !important;
  }
}
