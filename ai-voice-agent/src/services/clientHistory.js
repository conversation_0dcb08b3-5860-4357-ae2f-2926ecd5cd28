/**
 * Client History Service
 * Manages client data retrieval and analysis for personalized voice agent interactions
 */

export class ClientHistoryService {
  constructor(options = {}) {
    this.options = {
      apiBaseUrl: '/api/voice-agent',
      enableCaching: true,
      cacheTimeout: 5 * 60 * 1000, // 5 minutes
      ...options
    };

    // Cache for client data
    this.cache = new Map();
    
    // Event listeners
    this.eventListeners = new Map();
  }

  /**
   * Get client history from database
   */
  async getClientHistory(clientId) {
    try {
      console.log('📊 Fetching client history for:', clientId);
      
      // Check cache first
      if (this.options.enableCaching && this.cache.has(clientId)) {
        const cached = this.cache.get(clientId);
        if (Date.now() - cached.timestamp < this.options.cacheTimeout) {
          console.log('📋 Using cached client history');
          return cached.data;
        }
      }
      
      // Make API call to backend
      const response = await fetch(`${this.options.apiBaseUrl}/client-history/${encodeURIComponent(clientId)}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.message || 'Failed to fetch client history');
      }
      
      // Cache the result
      if (this.options.enableCaching) {
        this.cache.set(clientId, {
          data: result.data,
          timestamp: Date.now()
        });
      }
      
      console.log('✅ Client history fetched successfully');
      return result.data;
      
    } catch (error) {
      console.error('❌ Failed to fetch client history:', error);
      
      // Fallback to mock data if API fails
      console.warn('🔄 Falling back to mock data');
      return this.getMockClientHistory(clientId);
    }
  }

  /**
   * Update client preferences
   */
  async updateClientPreferences(clientId, preferences) {
    try {
      console.log('⚙️ Updating client preferences for:', clientId);
      
      const response = await fetch(`${this.options.apiBaseUrl}/preferences/${encodeURIComponent(clientId)}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(preferences)
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.message || 'Failed to update preferences');
      }
      
      // Clear cache for this client
      if (this.cache.has(clientId)) {
        this.cache.delete(clientId);
      }
      
      console.log('✅ Client preferences updated successfully');
      return result.data;
      
    } catch (error) {
      console.error('❌ Failed to update client preferences:', error);
      throw error;
    }
  }

  /**
   * Log conversation data
   */
  async logConversation(conversationData) {
    try {
      console.log('💬 Logging conversation data');
      
      const response = await fetch(`${this.options.apiBaseUrl}/conversation`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(conversationData)
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.message || 'Failed to log conversation');
      }
      
      console.log('✅ Conversation logged successfully');
      return result.data;
      
    } catch (error) {
      console.error('❌ Failed to log conversation:', error);
      // Don't throw error for logging failures
      return null;
    }
  }

  /**
   * Fallback mock data method
   */
  getMockClientHistory(clientId) {
    return {
      clientId: clientId,
      isNewClient: false,
      email: clientId,
      firstName: 'Valued Client',
      lastName: '',
      totalVisits: 3,
      lastVisit: new Date('2024-01-15'),
      daysSinceLastVisit: Math.floor((new Date() - new Date('2024-01-15')) / (1000 * 60 * 60 * 24)),
      preferredServices: ['Classic Haircut', 'Beard Trim'],
      averageSpending: 45,
      appointments: [
        {
          id: 'mock_001',
          date: new Date('2024-01-15'),
          service: 'Classic Haircut',
          price: 40,
          satisfaction: 5,
          notes: 'Great experience, loved the fade'
        },
        {
          id: 'mock_002',
          date: new Date('2023-12-10'),
          service: 'Beard Trim',
          price: 25,
          satisfaction: 4,
          notes: 'Good trim, maybe shorter next time'
        }
      ],
      preferences: {
        reminderDays: 28,
        preferredTime: 'afternoon',
        stylistPreference: 'Andre',
        voiceEnabled: true,
        preferredVoice: 'en-US-Standard-A',
        voiceSpeed: 1.0,
        reminderPreference: 'day_before'
      },
      personalizedGreeting: `Welcome back! It's been a while since your last Classic Haircut. Ready for another great experience?`,
      nextSuggestedService: 'Classic Haircut',
      estimatedNextVisit: new Date(Date.now() + 28 * 24 * 60 * 60 * 1000) // 28 days from now
    };
  }

  /**
   * Analyze client patterns for personalization
   */
  analyzeClientPatterns(clientHistory) {
    if (!clientHistory || !clientHistory.appointments) {
      return {
        visitFrequency: 'unknown',
        preferredServices: [],
        spendingPattern: 'unknown',
        loyaltyLevel: 'new'
      };
    }

    const appointments = clientHistory.appointments;
    
    // Calculate visit frequency
    let visitFrequency = 'irregular';
    if (appointments.length >= 2) {
      const intervals = [];
      for (let i = 1; i < appointments.length; i++) {
        const daysDiff = Math.floor((appointments[i-1].date - appointments[i].date) / (1000 * 60 * 60 * 24));
        intervals.push(daysDiff);
      }
      const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
      
      if (avgInterval <= 21) visitFrequency = 'frequent';
      else if (avgInterval <= 35) visitFrequency = 'regular';
      else if (avgInterval <= 60) visitFrequency = 'occasional';
    }

    // Determine loyalty level
    let loyaltyLevel = 'new';
    if (clientHistory.totalVisits >= 10) loyaltyLevel = 'vip';
    else if (clientHistory.totalVisits >= 5) loyaltyLevel = 'loyal';
    else if (clientHistory.totalVisits >= 2) loyaltyLevel = 'returning';

    // Spending pattern
    let spendingPattern = 'moderate';
    if (clientHistory.averageSpending >= 60) spendingPattern = 'premium';
    else if (clientHistory.averageSpending <= 30) spendingPattern = 'budget';

    return {
      visitFrequency,
      preferredServices: clientHistory.preferredServices || [],
      spendingPattern,
      loyaltyLevel,
      avgInterval: appointments.length >= 2 ? Math.floor(intervals.reduce((a, b) => a + b, 0) / intervals.length) : null
    };
  }

  /**
   * Clear cache
   */
  clearCache(clientId = null) {
    if (clientId) {
      this.cache.delete(clientId);
    } else {
      this.cache.clear();
    }
  }

  /**
   * Event emitter methods
   */
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(callback);
  }

  emit(event, data) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in event listener for ${event}:`, error);
        }
      });
    }
  }
}
