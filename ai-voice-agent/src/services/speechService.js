/**
 * SpeechService - Text-to-Speech functionality for AI Voice Agent
 * Handles voice synthesis, audio playback, and voice settings
 */

export class SpeechService {
  constructor(options = {}) {
    this.options = {
      defaultVoice: 'en-US-Standard-A',
      defaultSpeed: 1.0,
      defaultPitch: 1.0,
      defaultVolume: 0.8,
      enableWebSpeechAPI: true,
      enablePremiumTTS: false,
      premiumTTSApiKey: null,
      ...options
    };

    // State
    this.isInitialized = false;
    this.isPlaying = false;
    this.isPaused = false;
    this.currentAudio = null;
    this.currentUtterance = null;
    
    // Voice settings
    this.voiceSettings = {
      voice: this.options.defaultVoice,
      speed: this.options.defaultSpeed,
      pitch: this.options.defaultPitch,
      volume: this.options.defaultVolume
    };

    // Available voices
    this.availableVoices = [];
    this.webSpeechVoices = [];
    
    // Audio context for advanced features
    this.audioContext = null;
    this.analyser = null;
    
    // Event listeners
    this.eventListeners = new Map();
  }

  /**
   * Initialize the speech service
   */
  async init() {
    try {
      console.log('🎤 Initializing Speech Service...');
      
      // Initialize Web Speech API
      if (this.options.enableWebSpeechAPI && 'speechSynthesis' in window) {
        await this.initWebSpeechAPI();
      }
      
      // Initialize Audio Context for waveform analysis
      await this.initAudioContext();
      
      // Load available voices
      await this.loadAvailableVoices();
      
      this.isInitialized = true;
      console.log('✅ Speech Service initialized successfully');
      
    } catch (error) {
      console.error('❌ Failed to initialize Speech Service:', error);
      throw error;
    }
  }

  /**
   * Initialize Web Speech API
   */
  async initWebSpeechAPI() {
    return new Promise((resolve) => {
      // Wait for voices to be loaded
      const loadVoices = () => {
        this.webSpeechVoices = speechSynthesis.getVoices();
        if (this.webSpeechVoices.length > 0) {
          console.log(`📢 Loaded ${this.webSpeechVoices.length} Web Speech API voices`);
          resolve();
        } else {
          // Some browsers load voices asynchronously
          setTimeout(loadVoices, 100);
        }
      };

      // Listen for voice changes
      speechSynthesis.addEventListener('voiceschanged', loadVoices);
      
      // Initial load
      loadVoices();
    });
  }

  /**
   * Initialize Audio Context for advanced audio features
   */
  async initAudioContext() {
    try {
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
      this.analyser = this.audioContext.createAnalyser();
      this.analyser.fftSize = 256;
      
      console.log('🎵 Audio Context initialized for waveform analysis');
      
    } catch (error) {
      console.warn('⚠️ Audio Context not available:', error);
    }
  }

  /**
   * Load available voices from different sources
   */
  async loadAvailableVoices() {
    this.availableVoices = [];
    
    // Add Web Speech API voices
    this.webSpeechVoices.forEach(voice => {
      this.availableVoices.push({
        id: voice.name,
        name: voice.name,
        language: voice.lang,
        gender: this.detectGender(voice.name),
        source: 'webspeech',
        voice: voice
      });
    });
    
    // Add premium TTS voices (if enabled)
    if (this.options.enablePremiumTTS) {
      this.availableVoices.push(...this.getPremiumVoices());
    }
    
    console.log(`🎭 Loaded ${this.availableVoices.length} total voices`);
  }

  /**
   * Get premium TTS voices list
   */
  getPremiumVoices() {
    return [
      { id: 'en-US-Standard-A', name: 'English (US) - Female', language: 'en-US', gender: 'female', source: 'premium' },
      { id: 'en-US-Standard-B', name: 'English (US) - Male', language: 'en-US', gender: 'male', source: 'premium' },
      { id: 'en-US-Standard-C', name: 'English (US) - Female', language: 'en-US', gender: 'female', source: 'premium' },
      { id: 'en-US-Standard-D', name: 'English (US) - Male', language: 'en-US', gender: 'male', source: 'premium' },
      { id: 'en-US-Wavenet-A', name: 'English (US) - Female (Neural)', language: 'en-US', gender: 'female', source: 'premium' },
      { id: 'en-US-Wavenet-B', name: 'English (US) - Male (Neural)', language: 'en-US', gender: 'male', source: 'premium' },
    ];
  }

  /**
   * Synthesize text to speech
   */
  async synthesize(text, options = {}) {
    if (!this.isInitialized) {
      throw new Error('Speech service not initialized');
    }

    const settings = {
      ...this.voiceSettings,
      ...options
    };

    try {
      console.log('🗣️ Synthesizing speech:', text.substring(0, 50) + '...');
      
      // Choose synthesis method based on voice source
      const voice = this.findVoice(settings.voice);
      
      if (voice && voice.source === 'premium' && this.options.enablePremiumTTS) {
        return await this.synthesizeWithPremiumTTS(text, settings);
      } else {
        return await this.synthesizeWithWebSpeech(text, settings);
      }
      
    } catch (error) {
      console.error('❌ Speech synthesis failed:', error);
      throw error;
    }
  }

  /**
   * Synthesize with Web Speech API
   */
  async synthesizeWithWebSpeech(text, settings) {
    return new Promise((resolve, reject) => {
      const utterance = new SpeechSynthesisUtterance(text);
      
      // Find and set voice
      const voice = this.webSpeechVoices.find(v => 
        v.name === settings.voice || v.name.includes(settings.voice)
      ) || this.webSpeechVoices[0];
      
      if (voice) {
        utterance.voice = voice;
      }
      
      // Set voice parameters
      utterance.rate = settings.speed;
      utterance.pitch = settings.pitch;
      utterance.volume = settings.volume;
      
      // Event handlers
      utterance.onstart = () => {
        console.log('🎤 Speech started');
        this.emit('speechStart', { text, settings });
      };
      
      utterance.onend = () => {
        console.log('✅ Speech completed');
        resolve({
          duration: this.estimateDuration(text, settings.speed),
          waveform: this.generateMockWaveform(text),
          source: 'webspeech'
        });
      };
      
      utterance.onerror = (error) => {
        console.error('❌ Speech error:', error);
        reject(error);
      };
      
      // Store current utterance
      this.currentUtterance = utterance;
      
      // Start synthesis
      speechSynthesis.speak(utterance);
    });
  }

  /**
   * Synthesize with Premium TTS API
   */
  async synthesizeWithPremiumTTS(text, settings) {
    try {
      const response = await fetch('/api/tts/synthesize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.options.premiumTTSApiKey}`
        },
        body: JSON.stringify({
          text,
          voice: settings.voice,
          speed: settings.speed,
          pitch: settings.pitch,
          volume: settings.volume
        })
      });
      
      if (!response.ok) {
        throw new Error(`TTS API error: ${response.status}`);
      }
      
      const audioBlob = await response.blob();
      const audioUrl = URL.createObjectURL(audioBlob);
      
      return {
        audioUrl,
        duration: this.estimateDuration(text, settings.speed),
        waveform: await this.analyzeAudioWaveform(audioBlob),
        source: 'premium'
      };
      
    } catch (error) {
      console.warn('⚠️ Premium TTS failed, falling back to Web Speech API');
      return await this.synthesizeWithWebSpeech(text, settings);
    }
  }

  /**
   * Play synthesized audio
   */
  async play(audioData) {
    try {
      this.isPlaying = true;
      this.emit('playStart', audioData);
      
      if (audioData.source === 'webspeech') {
        // Web Speech API handles playback automatically
        await this.waitForWebSpeechCompletion();
      } else {
        // Play audio file
        await this.playAudioFile(audioData.audioUrl);
      }
      
      this.emit('playEnd', audioData);
      
    } catch (error) {
      console.error('❌ Audio playback failed:', error);
      throw error;
    } finally {
      this.isPlaying = false;
    }
  }

  /**
   * Play audio file
   */
  async playAudioFile(audioUrl) {
    return new Promise((resolve, reject) => {
      const audio = new Audio(audioUrl);
      
      // Connect to analyser for waveform analysis
      if (this.audioContext && this.analyser) {
        const source = this.audioContext.createMediaElementSource(audio);
        source.connect(this.analyser);
        this.analyser.connect(this.audioContext.destination);
      }
      
      audio.onended = () => {
        resolve();
      };
      
      audio.onerror = (error) => {
        reject(error);
      };
      
      this.currentAudio = audio;
      audio.play();
    });
  }

  /**
   * Wait for Web Speech API completion
   */
  async waitForWebSpeechCompletion() {
    return new Promise((resolve) => {
      const checkCompletion = () => {
        if (!speechSynthesis.speaking) {
          resolve();
        } else {
          setTimeout(checkCompletion, 100);
        }
      };
      checkCompletion();
    });
  }

  /**
   * Stop current speech
   */
  async stop() {
    try {
      if (this.currentAudio) {
        this.currentAudio.pause();
        this.currentAudio.currentTime = 0;
        this.currentAudio = null;
      }
      
      if (speechSynthesis.speaking) {
        speechSynthesis.cancel();
      }
      
      this.isPlaying = false;
      this.isPaused = false;
      
      this.emit('stopped');
      
    } catch (error) {
      console.error('❌ Failed to stop speech:', error);
    }
  }

  /**
   * Pause current speech
   */
  pause() {
    try {
      if (this.currentAudio) {
        this.currentAudio.pause();
      }
      
      if (speechSynthesis.speaking) {
        speechSynthesis.pause();
      }
      
      this.isPaused = true;
      this.emit('paused');
      
    } catch (error) {
      console.error('❌ Failed to pause speech:', error);
    }
  }

  /**
   * Resume paused speech
   */
  resume() {
    try {
      if (this.currentAudio && this.isPaused) {
        this.currentAudio.play();
      }
      
      if (speechSynthesis.paused) {
        speechSynthesis.resume();
      }
      
      this.isPaused = false;
      this.emit('resumed');
      
    } catch (error) {
      console.error('❌ Failed to resume speech:', error);
    }
  }

  /**
   * Update voice settings
   */
  async updateVoiceSettings(settings) {
    this.voiceSettings = {
      ...this.voiceSettings,
      ...settings
    };
    
    console.log('🔧 Voice settings updated:', this.voiceSettings);
    this.emit('settingsUpdated', this.voiceSettings);
  }

  /**
   * Get current waveform data for animation
   */
  getWaveformData() {
    if (!this.analyser) return null;
    
    const bufferLength = this.analyser.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);
    this.analyser.getByteFrequencyData(dataArray);
    
    return dataArray;
  }

  /**
   * Utility methods
   */
  findVoice(voiceId) {
    return this.availableVoices.find(v => v.id === voiceId || v.name === voiceId);
  }

  detectGender(voiceName) {
    const femaleIndicators = ['female', 'woman', 'girl', 'lady'];
    const maleIndicators = ['male', 'man', 'boy', 'gentleman'];
    
    const name = voiceName.toLowerCase();
    
    if (femaleIndicators.some(indicator => name.includes(indicator))) {
      return 'female';
    }
    
    if (maleIndicators.some(indicator => name.includes(indicator))) {
      return 'male';
    }
    
    return 'neutral';
  }

  estimateDuration(text, speed = 1.0) {
    // Rough estimation: average speaking rate is 150-160 words per minute
    const wordsPerMinute = 155 * speed;
    const wordCount = text.split(' ').length;
    return (wordCount / wordsPerMinute) * 60 * 1000; // Return in milliseconds
  }

  generateMockWaveform(text) {
    // Generate a mock waveform for animation synchronization
    const length = Math.min(text.length, 100);
    const waveform = [];
    
    for (let i = 0; i < length; i++) {
      const amplitude = Math.sin(i * 0.1) * 0.5 + 0.5;
      waveform.push(amplitude);
    }
    
    return waveform;
  }

  async analyzeAudioWaveform(audioBlob) {
    // Analyze actual audio waveform (simplified implementation)
    try {
      const arrayBuffer = await audioBlob.arrayBuffer();
      const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);
      
      const channelData = audioBuffer.getChannelData(0);
      const samples = 100; // Sample points for animation
      const blockSize = Math.floor(channelData.length / samples);
      const waveform = [];
      
      for (let i = 0; i < samples; i++) {
        const start = i * blockSize;
        const end = start + blockSize;
        let sum = 0;
        
        for (let j = start; j < end; j++) {
          sum += Math.abs(channelData[j]);
        }
        
        waveform.push(sum / blockSize);
      }
      
      return waveform;
      
    } catch (error) {
      console.warn('⚠️ Waveform analysis failed, using mock data');
      return this.generateMockWaveform('audio');
    }
  }

  /**
   * Event system
   */
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(callback);
  }

  emit(event, data) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Error in speech service event listener:', error);
        }
      });
    }
  }

  /**
   * Get service status
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      isPlaying: this.isPlaying,
      isPaused: this.isPaused,
      availableVoices: this.availableVoices.length,
      currentSettings: this.voiceSettings,
      hasAudioContext: !!this.audioContext,
      hasWebSpeechAPI: 'speechSynthesis' in window
    };
  }

  /**
   * Cleanup
   */
  destroy() {
    this.stop();
    
    if (this.audioContext) {
      this.audioContext.close();
    }
    
    this.eventListeners.clear();
  }
}

export default SpeechService;
