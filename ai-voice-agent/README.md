# AI Voice Agent for Barber Brothers Legacy

## 🎯 Project Overview

This AI Voice Agent feature provides personalized greetings and contextual assistance for the Barber Brothers Legacy website. The agent activates immediately upon Google OAuth login, offering tailored experiences based on client history with a synchronized pulsing ball animation.

## ✨ Key Features

- **Instant Activation**: Triggers automatically upon successful Google OAuth login
- **Personalized Greetings**: Addresses users by name with contextual welcome messages
- **Client History Analysis**: Analyzes previous visits, services, and preferences
- **Contextual Conversations**: Different flows for new vs. returning clients
- **Synchronized Animation**: Smooth pulsing ball that syncs with speech patterns
- **Accessibility Compliant**: Respects motion sensitivity preferences
- **Mobile Optimized**: Responsive design for all devices

## 🏗️ Architecture

### Frontend Components
- **VoiceAgent.js**: Main voice agent controller
- **PulsingBall.js**: Speech-synchronized animation component
- **AuthHandler.js**: Integration with existing Google OAuth
- **ConversationFlow.js**: Manages conversation logic and state

### Backend Services
- **speechService.js**: Text-to-speech functionality
- **clientHistory.js**: Client data retrieval and analysis
- **aiLogic.js**: Conversation flow and personalization logic
- **animationService.js**: Animation timing and synchronization

### Database Schema
- **clients**: Client profiles and registration data
- **appointments**: Service history and appointment records
- **conversations**: Session logs and preference updates

## 🚀 Technology Stack

- **Frontend**: Vanilla JavaScript (ES6+) with Web Speech API
- **Backend**: Node.js/Express (integrates with existing server)
- **Database**: MongoDB (extends existing community-hub structure)
- **Authentication**: Google OAuth 2.0 (existing implementation)
- **Animation**: CSS3 transforms + JavaScript timing
- **Voice**: Web Speech API + premium TTS service

## 📁 Project Structure

```
ai-voice-agent/
├── README.md
├── package.json
├── .vscode/
│   ├── settings.json
│   ├── extensions.json
│   └── tasks.json
├── src/
│   ├── components/
│   │   ├── VoiceAgent.js
│   │   ├── PulsingBall.js
│   │   ├── AuthHandler.js
│   │   └── ConversationFlow.js
│   ├── services/
│   │   ├── speechService.js
│   │   ├── animationService.js
│   │   ├── clientHistory.js
│   │   └── aiLogic.js
│   ├── styles/
│   │   ├── pulsingBall.css
│   │   └── voiceAgent.css
│   ├── database/
│   │   ├── schema.sql
│   │   └── migrations/
│   └── utils/
│       ├── voiceUtils.js
│       ├── animationUtils.js
│       └── dateUtils.js
├── backend/
│   ├── api/
│   │   ├── auth.js
│   │   ├── clients.js
│   │   └── appointments.js
│   ├── models/
│   └── middleware/
├── tests/
│   ├── unit/
│   ├── integration/
│   └── performance/
└── documentation/
    ├── setup-guide.md
    ├── vscode-setup.md
    ├── api-documentation.md
    └── deployment-guide.md
```

## 🔧 Development Setup

### Prerequisites
- Node.js 16+
- Visual Studio Code
- AI Coding Assistant (GitHub Copilot, Cursor AI, or Claude Dev)

### Installation
1. Clone the repository
2. Install dependencies: `npm install`
3. Configure VS Code workspace
4. Set up environment variables
5. Run development server: `npm run dev`

## 🎨 Pulsing Ball Animation

The pulsing ball animation is a key visual element that:
- Syncs perfectly with AI agent speech
- Provides visual feedback during conversations
- Respects accessibility preferences
- Maintains 60fps performance
- Offers customizable appearance

### Animation Features
- **Size**: 20-40px diameter (configurable)
- **Colors**: Brand-appropriate, customizable
- **Performance**: Optimized for minimal CPU usage
- **Accessibility**: Honors `prefers-reduced-motion`
- **Placement**: Non-intrusive corner/sidebar positioning

## 🔐 Security & Privacy

- **Data Protection**: Secure client information handling
- **GDPR Compliance**: Proper consent and data management
- **Session Security**: Secure authentication state
- **Voice Privacy**: No permanent storage of voice interactions
- **Token Security**: JWT-based authentication

## 📊 Success Metrics

- Seamless login-to-voice activation flow
- Accurate client history retrieval and personalization
- Increased appointment bookings through AI assistance
- Positive user feedback on voice interaction quality
- Reduced time-to-booking for returning clients

## 🧪 Testing

- **Unit Tests**: Core functionality testing
- **Integration Tests**: End-to-end flow testing
- **Performance Tests**: Animation and voice performance
- **Accessibility Tests**: Motion sensitivity compliance
- **Cross-browser Tests**: Compatibility verification

## 📚 Documentation

- [Setup Guide](documentation/setup-guide.md)
- [VS Code Configuration](documentation/vscode-setup.md)
- [API Documentation](documentation/api-documentation.md)
- [Deployment Guide](documentation/deployment-guide.md)

## 🚀 Deployment

The AI Voice Agent integrates seamlessly with the existing Barber Brothers Legacy website infrastructure, extending the current Google OAuth and Firebase setup.

## 📄 License

MIT License - See LICENSE file for details

## 👥 Contributors

- Andre The Barber - Lead Developer
- AI Assistant - Development Support

---

**Barber Brothers Legacy** - Premium Cuts by Andre The Barber
