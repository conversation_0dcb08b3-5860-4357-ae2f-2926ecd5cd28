/**
 * Firebase Services Utility
 * Centralized Firebase service functions for Barber Brothers Legacy
 * Provides authentication, database, and storage operations
 */

// Import Firebase configuration
import { auth, db, storage, analytics } from './firebase-config.js';
import { 
    signInWithPopup, 
    GoogleAuthProvider, 
    signOut, 
    onAuthStateChanged,
    createUserWithEmailAndPassword,
    signInWithEmailAndPassword,
    sendPasswordResetEmail,
    updateProfile
} from "https://www.gstatic.com/firebasejs/10.14.1/firebase-auth.js";

import {
    collection,
    doc,
    addDoc,
    getDoc,
    getDocs,
    updateDoc,
    deleteDoc,
    query,
    where,
    orderBy,
    limit,
    onSnapshot,
    serverTimestamp
} from "https://www.gstatic.com/firebasejs/10.14.1/firebase-firestore.js";

import {
    ref,
    uploadBytes,
    uploadBytesResumable,
    getDownloadURL,
    deleteObject
} from "https://www.gstatic.com/firebasejs/10.14.1/firebase-storage.js";

// Authentication Services
export class AuthService {
    constructor() {
        this.currentUser = null;
        this.authStateListeners = [];
        this.setupAuthStateListener();
    }

    // Setup authentication state listener
    setupAuthStateListener() {
        onAuthStateChanged(auth, (user) => {
            this.currentUser = user;
            console.log('🔐 Auth state changed:', user ? user.email : 'No user');
            
            // Notify all listeners
            this.authStateListeners.forEach(callback => {
                try {
                    callback(user);
                } catch (error) {
                    console.error('❌ Auth state listener error:', error);
                }
            });

            // Dispatch custom event for global listening
            window.dispatchEvent(new CustomEvent('authStateChanged', {
                detail: { user, isAuthenticated: !!user }
            }));
        });
    }

    // Add auth state listener
    addAuthStateListener(callback) {
        this.authStateListeners.push(callback);
        // Immediately call with current state
        if (this.currentUser !== null) {
            callback(this.currentUser);
        }
    }

    // Remove auth state listener
    removeAuthStateListener(callback) {
        const index = this.authStateListeners.indexOf(callback);
        if (index > -1) {
            this.authStateListeners.splice(index, 1);
        }
    }

    // Google Sign In
    async signInWithGoogle() {
        try {
            const provider = new GoogleAuthProvider();
            provider.addScope('email');
            provider.addScope('profile');
            
            const result = await signInWithPopup(auth, provider);
            console.log('✅ Google sign-in successful:', result.user.email);
            return result.user;
        } catch (error) {
            console.error('❌ Google sign-in error:', error);
            throw new Error(`Google sign-in failed: ${error.message}`);
        }
    }

    // Email/Password Sign Up
    async signUpWithEmail(email, password, displayName) {
        try {
            const result = await createUserWithEmailAndPassword(auth, email, password);
            
            // Update profile with display name
            if (displayName) {
                await updateProfile(result.user, { displayName });
            }
            
            console.log('✅ Email sign-up successful:', result.user.email);
            return result.user;
        } catch (error) {
            console.error('❌ Email sign-up error:', error);
            throw new Error(`Sign-up failed: ${error.message}`);
        }
    }

    // Email/Password Sign In
    async signInWithEmail(email, password) {
        try {
            const result = await signInWithEmailAndPassword(auth, email, password);
            console.log('✅ Email sign-in successful:', result.user.email);
            return result.user;
        } catch (error) {
            console.error('❌ Email sign-in error:', error);
            throw new Error(`Sign-in failed: ${error.message}`);
        }
    }

    // Password Reset
    async resetPassword(email) {
        try {
            await sendPasswordResetEmail(auth, email);
            console.log('✅ Password reset email sent to:', email);
            return true;
        } catch (error) {
            console.error('❌ Password reset error:', error);
            throw new Error(`Password reset failed: ${error.message}`);
        }
    }

    // Sign Out
    async signOut() {
        try {
            await signOut(auth);
            console.log('✅ User signed out successfully');
            return true;
        } catch (error) {
            console.error('❌ Sign-out error:', error);
            throw new Error(`Sign-out failed: ${error.message}`);
        }
    }

    // Get current user
    getCurrentUser() {
        return this.currentUser;
    }

    // Check if user is authenticated
    isAuthenticated() {
        return !!this.currentUser;
    }
}

// Database Services
export class DatabaseService {
    constructor() {
        this.db = db;
    }

    // Create document
    async createDocument(collectionName, data) {
        try {
            const docRef = await addDoc(collection(this.db, collectionName), {
                ...data,
                createdAt: serverTimestamp(),
                updatedAt: serverTimestamp()
            });
            console.log('✅ Document created with ID:', docRef.id);
            return docRef.id;
        } catch (error) {
            console.error('❌ Create document error:', error);
            throw new Error(`Failed to create document: ${error.message}`);
        }
    }

    // Get document by ID
    async getDocument(collectionName, docId) {
        try {
            const docRef = doc(this.db, collectionName, docId);
            const docSnap = await getDoc(docRef);
            
            if (docSnap.exists()) {
                return { id: docSnap.id, ...docSnap.data() };
            } else {
                return null;
            }
        } catch (error) {
            console.error('❌ Get document error:', error);
            throw new Error(`Failed to get document: ${error.message}`);
        }
    }

    // Update document
    async updateDocument(collectionName, docId, data) {
        try {
            const docRef = doc(this.db, collectionName, docId);
            await updateDoc(docRef, {
                ...data,
                updatedAt: serverTimestamp()
            });
            console.log('✅ Document updated:', docId);
            return true;
        } catch (error) {
            console.error('❌ Update document error:', error);
            throw new Error(`Failed to update document: ${error.message}`);
        }
    }

    // Delete document
    async deleteDocument(collectionName, docId) {
        try {
            const docRef = doc(this.db, collectionName, docId);
            await deleteDoc(docRef);
            console.log('✅ Document deleted:', docId);
            return true;
        } catch (error) {
            console.error('❌ Delete document error:', error);
            throw new Error(`Failed to delete document: ${error.message}`);
        }
    }

    // Query documents
    async queryDocuments(collectionName, conditions = [], orderByField = null, limitCount = null) {
        try {
            let q = collection(this.db, collectionName);
            
            // Apply where conditions
            conditions.forEach(condition => {
                q = query(q, where(condition.field, condition.operator, condition.value));
            });
            
            // Apply ordering
            if (orderByField) {
                q = query(q, orderBy(orderByField.field, orderByField.direction || 'asc'));
            }
            
            // Apply limit
            if (limitCount) {
                q = query(q, limit(limitCount));
            }
            
            const querySnapshot = await getDocs(q);
            const documents = [];
            querySnapshot.forEach((doc) => {
                documents.push({ id: doc.id, ...doc.data() });
            });
            
            return documents;
        } catch (error) {
            console.error('❌ Query documents error:', error);
            throw new Error(`Failed to query documents: ${error.message}`);
        }
    }

    // Listen to document changes
    listenToDocument(collectionName, docId, callback) {
        const docRef = doc(this.db, collectionName, docId);
        return onSnapshot(docRef, (doc) => {
            if (doc.exists()) {
                callback({ id: doc.id, ...doc.data() });
            } else {
                callback(null);
            }
        }, (error) => {
            console.error('❌ Document listener error:', error);
            callback(null, error);
        });
    }

    // Listen to collection changes
    listenToCollection(collectionName, conditions = [], orderByField = null, limitCount = null, callback) {
        try {
            let q = collection(this.db, collectionName);
            
            // Apply conditions
            conditions.forEach(condition => {
                q = query(q, where(condition.field, condition.operator, condition.value));
            });
            
            if (orderByField) {
                q = query(q, orderBy(orderByField.field, orderByField.direction || 'asc'));
            }
            
            if (limitCount) {
                q = query(q, limit(limitCount));
            }
            
            return onSnapshot(q, (querySnapshot) => {
                const documents = [];
                querySnapshot.forEach((doc) => {
                    documents.push({ id: doc.id, ...doc.data() });
                });
                callback(documents);
            }, (error) => {
                console.error('❌ Collection listener error:', error);
                callback([], error);
            });
        } catch (error) {
            console.error('❌ Listen to collection error:', error);
            throw new Error(`Failed to listen to collection: ${error.message}`);
        }
    }
}

// Storage Services
export class StorageService {
    constructor() {
        this.storage = storage;
    }

    // Upload file
    async uploadFile(file, path, onProgress = null) {
        try {
            const storageRef = ref(this.storage, path);
            
            if (onProgress) {
                // Use resumable upload for progress tracking
                const uploadTask = uploadBytesResumable(storageRef, file);
                
                return new Promise((resolve, reject) => {
                    uploadTask.on('state_changed',
                        (snapshot) => {
                            const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
                            onProgress(progress);
                        },
                        (error) => {
                            console.error('❌ Upload error:', error);
                            reject(new Error(`Upload failed: ${error.message}`));
                        },
                        async () => {
                            try {
                                const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
                                console.log('✅ File uploaded successfully:', downloadURL);
                                resolve(downloadURL);
                            } catch (error) {
                                reject(error);
                            }
                        }
                    );
                });
            } else {
                // Simple upload without progress
                const snapshot = await uploadBytes(storageRef, file);
                const downloadURL = await getDownloadURL(snapshot.ref);
                console.log('✅ File uploaded successfully:', downloadURL);
                return downloadURL;
            }
        } catch (error) {
            console.error('❌ Upload file error:', error);
            throw new Error(`Failed to upload file: ${error.message}`);
        }
    }

    // Delete file
    async deleteFile(path) {
        try {
            const storageRef = ref(this.storage, path);
            await deleteObject(storageRef);
            console.log('✅ File deleted successfully:', path);
            return true;
        } catch (error) {
            console.error('❌ Delete file error:', error);
            throw new Error(`Failed to delete file: ${error.message}`);
        }
    }

    // Get download URL
    async getDownloadURL(path) {
        try {
            const storageRef = ref(this.storage, path);
            const url = await getDownloadURL(storageRef);
            return url;
        } catch (error) {
            console.error('❌ Get download URL error:', error);
            throw new Error(`Failed to get download URL: ${error.message}`);
        }
    }
}

// Create singleton instances
export const authService = new AuthService();
export const databaseService = new DatabaseService();
export const storageService = new StorageService();

// Global access for legacy compatibility
window.FirebaseServices = {
    auth: authService,
    database: databaseService,
    storage: storageService
};

console.log('🔥 Firebase services initialized successfully');
