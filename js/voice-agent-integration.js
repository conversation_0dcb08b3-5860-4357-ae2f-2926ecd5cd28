/**
 * Voice Agent Integration Script
 * Main integration file for the Barber Brothers AI Voice Agent
 * This file loads and initializes the voice agent system
 */

(function() {
    'use strict';

    // Configuration
    const VOICE_AGENT_CONFIG = {
        enabled: true,
        autoActivateOnLogin: true,
        enableDebugMode: false,
        basePath: '/ai-voice-agent/src/',
        cssPath: '/ai-voice-agent/src/styles/',
        enableAnimation: true,
        enableVoice: true
    };

    // Global voice agent instance
    window.voiceAgentBridge = null;

    /**
     * Load CSS files dynamically
     */
    function loadCSS(href) {
        return new Promise((resolve, reject) => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = href;
            link.onload = resolve;
            link.onerror = reject;
            document.head.appendChild(link);
        });
    }

    /**
     * Load JavaScript files dynamically
     */
    function loadScript(src, type = 'text/javascript') {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.type = type;
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    /**
     * Load voice agent assets
     */
    async function loadVoiceAgentAssets() {
        try {
            console.log('🎤 Loading Voice Agent assets...');

            // Load CSS files
            await Promise.all([
                loadCSS(VOICE_AGENT_CONFIG.cssPath + 'voiceAgent.css'),
                loadCSS(VOICE_AGENT_CONFIG.cssPath + 'pulsingBall.css')
            ]);

            console.log('✅ Voice Agent CSS loaded');

            // Load JavaScript modules (in order)
            const scriptPaths = [
                // Utilities first
                VOICE_AGENT_CONFIG.basePath + 'utils/voiceUtils.js',
                VOICE_AGENT_CONFIG.basePath + 'utils/animationUtils.js',
                VOICE_AGENT_CONFIG.basePath + 'utils/dateUtils.js',
                
                // Services
                VOICE_AGENT_CONFIG.basePath + 'services/speechService.js',
                VOICE_AGENT_CONFIG.basePath + 'services/animationService.js',
                VOICE_AGENT_CONFIG.basePath + 'services/clientHistory.js',
                VOICE_AGENT_CONFIG.basePath + 'services/aiLogic.js',
                
                // Components
                VOICE_AGENT_CONFIG.basePath + 'components/PulsingBall.js',
                VOICE_AGENT_CONFIG.basePath + 'components/ConversationFlow.js',
                VOICE_AGENT_CONFIG.basePath + 'components/VoiceAgent.js',
                
                // Integration bridge
                VOICE_AGENT_CONFIG.basePath + 'integration/voiceAgentBridge.js'
            ];

            // Load scripts sequentially to maintain dependencies
            for (const scriptPath of scriptPaths) {
                try {
                    await loadScript(scriptPath, 'module');
                    console.log(`✅ Loaded: ${scriptPath}`);
                } catch (error) {
                    console.warn(`⚠️ Failed to load: ${scriptPath}`, error);
                }
            }

            console.log('✅ Voice Agent JavaScript modules loaded');
            return true;

        } catch (error) {
            console.error('❌ Failed to load Voice Agent assets:', error);
            return false;
        }
    }

    /**
     * Initialize voice agent bridge
     */
    async function initializeVoiceAgent() {
        try {
            console.log('🚀 Initializing Voice Agent Bridge...');

            // Check if VoiceAgentBridge is available
            if (typeof window.VoiceAgentBridge === 'undefined') {
                throw new Error('VoiceAgentBridge not loaded');
            }

            // Create voice agent bridge instance
            window.voiceAgentBridge = new window.VoiceAgentBridge({
                autoActivateOnLogin: VOICE_AGENT_CONFIG.autoActivateOnLogin,
                enableDebugMode: VOICE_AGENT_CONFIG.enableDebugMode,
                apiBaseUrl: '/api'
            });

            // Set up event listeners
            window.voiceAgentBridge.on('voiceAgentActivated', (data) => {
                console.log('🎉 Voice Agent activated for:', data.user.email);
                
                // Dispatch custom event for other parts of the application
                window.dispatchEvent(new CustomEvent('voiceAgentReady', {
                    detail: { user: data.user, bridge: window.voiceAgentBridge }
                }));
            });

            window.voiceAgentBridge.on('voiceAgentError', (error) => {
                console.error('🚨 Voice Agent error:', error);
            });

            console.log('✅ Voice Agent Bridge initialized successfully');
            return true;

        } catch (error) {
            console.error('❌ Failed to initialize Voice Agent:', error);
            return false;
        }
    }

    /**
     * Check if voice agent should be enabled
     */
    function shouldEnableVoiceAgent() {
        // Check configuration
        if (!VOICE_AGENT_CONFIG.enabled) {
            return false;
        }

        // Check browser support
        if (!('speechSynthesis' in window)) {
            console.warn('⚠️ Speech synthesis not supported in this browser');
            return false;
        }

        // Check if user has disabled voice features
        const userPreference = localStorage.getItem('voiceAgentEnabled');
        if (userPreference === 'false') {
            console.log('🔇 Voice agent disabled by user preference');
            return false;
        }

        return true;
    }

    /**
     * Main initialization function
     */
    async function initVoiceAgentIntegration() {
        try {
            console.log('🎤 Starting Voice Agent Integration...');

            // Check if voice agent should be enabled
            if (!shouldEnableVoiceAgent()) {
                console.log('🔇 Voice Agent integration skipped');
                return;
            }

            // Load assets
            const assetsLoaded = await loadVoiceAgentAssets();
            if (!assetsLoaded) {
                throw new Error('Failed to load Voice Agent assets');
            }

            // Wait a bit for modules to be available
            await new Promise(resolve => setTimeout(resolve, 100));

            // Initialize voice agent
            const initialized = await initializeVoiceAgent();
            if (!initialized) {
                throw new Error('Failed to initialize Voice Agent');
            }

            console.log('🎉 Voice Agent Integration completed successfully!');

            // Dispatch ready event
            window.dispatchEvent(new CustomEvent('voiceAgentIntegrationReady', {
                detail: { bridge: window.voiceAgentBridge }
            }));

        } catch (error) {
            console.error('❌ Voice Agent Integration failed:', error);
            
            // Dispatch error event
            window.dispatchEvent(new CustomEvent('voiceAgentIntegrationError', {
                detail: { error: error.message }
            }));
        }
    }

    /**
     * Utility functions for external access
     */
    window.VoiceAgentIntegration = {
        // Get the bridge instance
        getBridge: () => window.voiceAgentBridge,
        
        // Check if voice agent is active
        isActive: () => window.voiceAgentBridge && window.voiceAgentBridge.isActive(),
        
        // Manually activate voice agent
        activate: () => window.voiceAgentBridge && window.voiceAgentBridge.activate(),
        
        // Manually deactivate voice agent
        deactivate: () => window.voiceAgentBridge && window.voiceAgentBridge.deactivate(),
        
        // Get current user
        getCurrentUser: () => window.voiceAgentBridge && window.voiceAgentBridge.getCurrentUser(),
        
        // Enable/disable voice agent
        setEnabled: (enabled) => {
            localStorage.setItem('voiceAgentEnabled', enabled.toString());
            if (!enabled && window.voiceAgentBridge) {
                window.voiceAgentBridge.deactivate();
            }
        },
        
        // Check if enabled
        isEnabled: () => localStorage.getItem('voiceAgentEnabled') !== 'false'
    };

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initVoiceAgentIntegration);
    } else {
        // DOM is already ready
        setTimeout(initVoiceAgentIntegration, 100);
    }

    console.log('🎤 Voice Agent Integration script loaded');

})();
