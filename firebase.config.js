/**
 * Firebase Configuration
 * Centralized Firebase setup for Barber Brothers Legacy
 * Supports both development and production environments
 */

// Import the functions you need from the SDKs you need
import { initializeApp, getApps } from "firebase/app";
import { getAuth, connectAuthEmulator } from "firebase/auth";
import { getFirestore, connectFirestoreEmulator } from "firebase/firestore";
import { getStorage, connectStorageEmulator } from "firebase/storage";
import { getAnalytics, isSupported } from "firebase/analytics";

// Environment-based configuration
const getFirebaseConfig = () => {
  // Check if we're in a browser environment
  const isBrowser = typeof window !== 'undefined';

  // Get environment variables (works in both Node.js and browser with build tools)
  const config = {
    apiKey: process.env.REACT_APP_FIREBASE_API_KEY ||
            (isBrowser && window.FIREBASE_API_KEY) ||
            "AIzaSyAHqpO8PPXmZIfype7dsViz3chKcmLdmpY",
    authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN ||
                "barber-brothers-legacy.firebaseapp.com",
    projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID ||
               "barber-brothers-legacy",
    storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET ||
                   "barber-brothers-legacy.appspot.com",
    messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID ||
                       "946338896038",
    appId: process.env.REACT_APP_FIREBASE_APP_ID ||
           "1:946338896038:web:d65f5bef7973127d1abc67",
    measurementId: process.env.REACT_APP_FIREBASE_MEASUREMENT_ID ||
                   "G-XXXXXXXXXX"
  };

  return config;
};

// Initialize Firebase app (prevent multiple initialization)
let app;
const firebaseConfig = getFirebaseConfig();

if (!getApps().length) {
  app = initializeApp(firebaseConfig);
  console.log('✅ Firebase initialized successfully');
} else {
  app = getApps()[0];
  console.log('✅ Firebase app already initialized');
}

// Initialize Firebase services
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);

// Initialize Analytics (only in browser and if supported)
let analytics = null;
if (typeof window !== 'undefined') {
  isSupported().then((supported) => {
    if (supported) {
      analytics = getAnalytics(app);
      console.log('✅ Firebase Analytics initialized');
    }
  }).catch((error) => {
    console.warn('⚠️ Firebase Analytics not supported:', error);
  });
}

export { analytics };

// Development emulator setup
const isDevelopment = process.env.NODE_ENV === 'development';
const useEmulators = process.env.REACT_APP_USE_FIREBASE_EMULATORS === 'true';

if (isDevelopment && useEmulators && typeof window !== 'undefined') {
  // Connect to emulators if in development mode
  try {
    connectAuthEmulator(auth, 'http://localhost:9099', { disableWarnings: true });
    connectFirestoreEmulator(db, 'localhost', 8080);
    connectStorageEmulator(storage, 'localhost', 9199);
    console.log('🔧 Connected to Firebase emulators');
  } catch (error) {
    console.warn('⚠️ Could not connect to Firebase emulators:', error);
  }
}

// Export the app instance
export default app;

// Export configuration for debugging
export const firebaseConfigDebug = {
  projectId: firebaseConfig.projectId,
  authDomain: firebaseConfig.authDomain,
  isDevelopment,
  useEmulators,
  timestamp: new Date().toISOString()
};