rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    
    // User profile images
    // Users can upload/update their own profile images
    match /users/{userId}/profile/{allPaths=**} {
      allow read: if true; // Profile images are public
      allow write: if request.auth != null && 
        request.auth.uid == userId &&
        request.resource.size < 5 * 1024 * 1024 && // 5MB limit
        request.resource.contentType.matches('image/.*');
    }
    
    // User cover images
    match /users/{userId}/cover/{allPaths=**} {
      allow read: if true; // Cover images are public
      allow write: if request.auth != null && 
        request.auth.uid == userId &&
        request.resource.size < 10 * 1024 * 1024 && // 10MB limit
        request.resource.contentType.matches('image/.*');
    }
    
    // Post images
    // Authenticated users can upload post images
    match /posts/{postId}/{allPaths=**} {
      allow read: if true; // Post images are public
      allow write: if request.auth != null &&
        request.resource.size < 10 * 1024 * 1024 && // 10MB limit
        request.resource.contentType.matches('image/.*');
      allow delete: if request.auth != null &&
        (resource.metadata.uploadedBy == request.auth.uid ||
         isAdmin());
    }
    
    // Community uploads (general community content)
    match /community/{allPaths=**} {
      allow read: if true; // Community content is public
      allow write: if request.auth != null &&
        request.resource.size < 15 * 1024 * 1024 && // 15MB limit
        (request.resource.contentType.matches('image/.*') ||
         request.resource.contentType.matches('video/.*'));
      allow delete: if request.auth != null &&
        (resource.metadata.uploadedBy == request.auth.uid ||
         isAdmin());
    }
    
    // Temporary uploads (for processing)
    match /temp/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && 
        request.auth.uid == userId &&
        request.resource.size < 20 * 1024 * 1024; // 20MB limit
      allow delete: if request.auth != null && 
        request.auth.uid == userId;
    }
    
    // Admin uploads
    // Only admins can upload to admin directory
    match /admin/{allPaths=**} {
      allow read: if true; // Admin content can be public
      allow write, delete: if request.auth != null && 
        isAdmin() &&
        request.resource.size < 50 * 1024 * 1024; // 50MB limit for admin
    }
    
    // Backup directory (admin only)
    match /backups/{allPaths=**} {
      allow read, write, delete: if request.auth != null && 
        isAdmin();
    }
    
    // Helper function to check if user is admin
    function isAdmin() {
      return request.auth != null &&
        firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // Helper function to check file type
    function isImage() {
      return request.resource.contentType.matches('image/.*');
    }
    
    function isVideo() {
      return request.resource.contentType.matches('video/.*');
    }
    
    function isValidImageSize() {
      return request.resource.size < 10 * 1024 * 1024; // 10MB
    }
    
    function isValidVideoSize() {
      return request.resource.size < 100 * 1024 * 1024; // 100MB
    }
  }
}
