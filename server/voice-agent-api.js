/**
 * Voice Agent API Routes
 * Provides endpoints for the AI Voice Agent to access client data and history
 */

const express = require('express');
const router = express.Router();

// Import database models
const { VoiceAgentClient, VoiceAgentAppointment, VoiceAgentConversation } = require('./voice-agent-db-setup');

// Mock data for fallback - will be replaced with actual database queries
const mockClientData = {
  '<EMAIL>': {
    id: 'client_123',
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    totalVisits: 5,
    lastVisit: new Date('2024-06-15'),
    preferredServices: ['Classic Haircut', 'Beard Trim'],
    averageSpending: 45,
    appointments: [
      {
        id: 'apt_001',
        date: new Date('2024-06-15'),
        service: 'Classic Haircut',
        price: 40,
        satisfaction: 5,
        notes: 'Great cut, loved the fade'
      },
      {
        id: 'apt_002',
        date: new Date('2024-05-20'),
        service: 'Beard Trim',
        price: 25,
        satisfaction: 4,
        notes: 'Good trim, maybe a bit shorter next time'
      }
    ],
    preferences: {
      voiceEnabled: true,
      preferredVoice: 'en-US-Standard-A',
      voiceSpeed: 1.0,
      reminderPreference: 'day_before'
    }
  }
};

/**
 * @route   GET /api/voice-agent/client-history/:identifier
 * @desc    Get client history for voice agent personalization
 * @access  Public (will be secured later)
 * @param   {string} identifier - Email or Google ID
 */
router.get('/client-history/:identifier', async (req, res) => {
  try {
    const { identifier } = req.params;

    console.log(`🔍 Voice Agent: Fetching client history for ${identifier}`);

    // Try to find client in database
    let client = await VoiceAgentClient.findOne({
      $or: [
        { email: identifier },
        { googleId: identifier }
      ]
    });

    if (!client) {
      // Check if this is a Google user we should create
      if (identifier.includes('@')) {
        // Create new client record
        client = new VoiceAgentClient({
          email: identifier,
          firstName: 'Valued Client',
          lastName: '',
          totalVisits: 0
        });
        await client.save();
        console.log('✅ Created new client record for:', identifier);
      }

      // Return default structure for new clients
      return res.json({
        success: true,
        data: {
          isNewClient: true,
          id: client?._id || null,
          email: identifier,
          firstName: client?.firstName || 'Valued Client',
          lastName: client?.lastName || '',
          totalVisits: 0,
          lastVisit: null,
          preferredServices: [],
          averageSpending: 0,
          appointments: [],
          preferences: {
            voiceEnabled: true,
            preferredVoice: 'en-US-Standard-A',
            voiceSpeed: 1.0,
            reminderPreference: 'day_before'
          },
          personalizedGreeting: "Welcome to Barber Brothers Legacy! We're excited to meet you and provide you with an exceptional grooming experience."
        }
      });
    }

    // Get client's appointments
    const appointments = await VoiceAgentAppointment.find({
      clientId: client._id
    }).sort({ appointmentDate: -1 }).limit(10);

    // Calculate statistics
    const completedAppointments = appointments.filter(apt => apt.status === 'completed');
    const totalSpending = completedAppointments.reduce((sum, apt) => sum + (apt.totalAmount || 0), 0);
    const averageSpending = completedAppointments.length > 0 ? totalSpending / completedAppointments.length : 0;
    const lastVisit = completedAppointments.length > 0 ? completedAppointments[0].appointmentDate : null;
    
    // Calculate days since last visit
    const daysSinceLastVisit = lastVisit
      ? Math.floor((new Date() - lastVisit) / (1000 * 60 * 60 * 24))
      : null;

    // Generate personalized greeting
    let personalizedGreeting = `Welcome back, ${client.firstName}!`;

    if (daysSinceLastVisit) {
      if (daysSinceLastVisit <= 7) {
        personalizedGreeting += ` Great to see you again so soon!`;
      } else if (daysSinceLastVisit <= 30) {
        personalizedGreeting += ` It's been ${daysSinceLastVisit} days since your last visit.`;
      } else {
        personalizedGreeting += ` It's been a while - ${daysSinceLastVisit} days since your last ${appointments[0]?.serviceType || 'visit'}.`;
      }
    }

    // Add service suggestion based on history
    if (client.preferredServices.length > 0) {
      personalizedGreeting += ` Ready for another ${client.preferredServices[0]}?`;
    }

    const responseData = {
      id: client._id,
      email: client.email,
      firstName: client.firstName,
      lastName: client.lastName,
      isNewClient: false,
      totalVisits: client.totalVisits,
      lastVisit,
      daysSinceLastVisit,
      preferredServices: client.preferredServices,
      averageSpending,
      appointments: appointments.map(apt => ({
        id: apt._id,
        date: apt.appointmentDate,
        service: apt.serviceType,
        price: apt.totalAmount,
        satisfaction: apt.satisfactionRating,
        notes: apt.clientNotes
      })),
      preferences: {
        voiceEnabled: client.voiceEnabled,
        preferredVoice: client.preferredVoice,
        voiceSpeed: client.voiceSpeed,
        reminderPreference: client.reminderPreference
      },
      personalizedGreeting,
      nextSuggestedService: client.preferredServices[0] || 'Classic Haircut',
      estimatedNextVisit: calculateNextVisitSuggestion(appointments)
    };

    res.json({
      success: true,
      data: responseData
    });
    
  } catch (error) {
    console.error('❌ Voice Agent API Error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch client history',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @route   POST /api/voice-agent/conversation
 * @desc    Log voice agent conversation
 * @access  Public (will be secured later)
 */
router.post('/conversation', async (req, res) => {
  try {
    const { clientId, sessionId, conversationType, messages, metrics, outcome, clientSatisfaction } = req.body;

    console.log(`💬 Voice Agent: Logging conversation for client ${clientId}`);

    // Find or create client
    let client = await VoiceAgentClient.findOne({
      $or: [
        { _id: clientId },
        { email: clientId },
        { googleId: clientId }
      ]
    });

    if (!client && clientId.includes('@')) {
      // Create new client if email provided
      client = new VoiceAgentClient({
        email: clientId,
        firstName: 'Valued Client'
      });
      await client.save();
    }

    // Create conversation record
    const conversation = new VoiceAgentConversation({
      clientId: client?._id,
      sessionId,
      conversationType: conversationType || 'welcome',
      messages: messages || [],
      outcome: outcome || 'completed',
      clientSatisfaction,
      durationSeconds: metrics?.durationSeconds,
      conversationEnd: new Date()
    });

    await conversation.save();

    console.log('✅ Conversation logged to database');

    res.json({
      success: true,
      data: {
        conversationId: conversation._id,
        message: 'Conversation logged successfully'
      }
    });

  } catch (error) {
    console.error('❌ Voice Agent Conversation Log Error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to log conversation',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @route   PUT /api/voice-agent/preferences/:identifier
 * @desc    Update client voice preferences
 * @access  Public (will be secured later)
 */
router.put('/preferences/:identifier', async (req, res) => {
  try {
    const { identifier } = req.params;
    const { voiceEnabled, preferredVoice, voiceSpeed, reminderPreference } = req.body;
    
    console.log(`⚙️ Voice Agent: Updating preferences for ${identifier}`);
    
    // For now, update mock data
    if (mockClientData[identifier]) {
      mockClientData[identifier].preferences = {
        ...mockClientData[identifier].preferences,
        voiceEnabled: voiceEnabled !== undefined ? voiceEnabled : mockClientData[identifier].preferences.voiceEnabled,
        preferredVoice: preferredVoice || mockClientData[identifier].preferences.preferredVoice,
        voiceSpeed: voiceSpeed !== undefined ? voiceSpeed : mockClientData[identifier].preferences.voiceSpeed,
        reminderPreference: reminderPreference || mockClientData[identifier].preferences.reminderPreference
      };
    }
    
    res.json({
      success: true,
      data: {
        message: 'Preferences updated successfully',
        preferences: mockClientData[identifier]?.preferences || {}
      }
    });
    
  } catch (error) {
    console.error('❌ Voice Agent Preferences Update Error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update preferences',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @route   GET /api/voice-agent/analytics
 * @desc    Get voice agent analytics and metrics
 * @access  Public (will be secured later)
 */
router.get('/analytics', async (req, res) => {
  try {
    console.log('📊 Voice Agent: Fetching analytics');
    
    // Mock analytics data
    const analytics = {
      totalActivations: 150,
      successfulConversations: 142,
      averageConversationDuration: 45.5,
      topIntents: [
        { intent: 'booking', count: 85 },
        { intent: 'inquiry', count: 35 },
        { intent: 'greeting', count: 150 }
      ],
      userSatisfaction: 4.7,
      conversionRate: 0.68,
      lastUpdated: new Date()
    };
    
    res.json({
      success: true,
      data: analytics
    });
    
  } catch (error) {
    console.error('❌ Voice Agent Analytics Error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch analytics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * Helper function to calculate next visit suggestion
 */
function calculateNextVisitSuggestion(appointments) {
  if (!appointments || appointments.length === 0) {
    return null;
  }
  
  // Calculate average time between appointments
  if (appointments.length < 2) {
    // Default to 4 weeks for first-time clients
    const nextVisit = new Date();
    nextVisit.setDate(nextVisit.getDate() + 28);
    return nextVisit;
  }
  
  // Calculate average interval
  let totalDays = 0;
  for (let i = 1; i < appointments.length; i++) {
    const daysDiff = Math.floor((appointments[i-1].date - appointments[i].date) / (1000 * 60 * 60 * 24));
    totalDays += daysDiff;
  }
  
  const averageInterval = Math.floor(totalDays / (appointments.length - 1));
  
  // Suggest next visit based on average interval
  const nextVisit = new Date(appointments[0].date);
  nextVisit.setDate(nextVisit.getDate() + averageInterval);
  
  return nextVisit;
}

/**
 * Health check endpoint
 */
router.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Voice Agent API is healthy',
    timestamp: new Date(),
    version: '1.0.0'
  });
});

module.exports = router;
