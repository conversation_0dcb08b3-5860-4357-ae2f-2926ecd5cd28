/**
 * Voice Agent Database Setup
 * Sets up database tables and initial data for the AI Voice Agent system
 */

const mongoose = require('mongoose');
require('dotenv').config();

// Voice Agent Schemas
const clientSchema = new mongoose.Schema({
  // Basic Information (from Google OAuth)
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  googleId: {
    type: String,
    unique: true,
    sparse: true
  },
  firstName: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  lastName: {
    type: String,
    trim: true,
    maxlength: 100
  },
  displayName: {
    type: String,
    trim: true,
    maxlength: 200
  },
  phone: {
    type: String,
    trim: true,
    match: [/^\+?[\d\s\-\(\)]+$/, 'Please enter a valid phone number']
  },
  profilePicture: {
    type: String,
    default: null
  },

  // Registration and Activity
  registrationDate: {
    type: Date,
    default: Date.now
  },
  lastLoginAt: {
    type: Date,
    default: Date.now
  },
  totalVisits: {
    type: Number,
    default: 0
  },
  isActive: {
    type: Boolean,
    default: true
  },

  // Voice Agent Preferences
  voiceEnabled: {
    type: Boolean,
    default: true
  },
  preferredVoice: {
    type: String,
    default: 'en-US-Standard-A'
  },
  voiceSpeed: {
    type: Number,
    default: 1.0,
    min: 0.5,
    max: 2.0
  },
  voicePitch: {
    type: Number,
    default: 1.0,
    min: 0.5,
    max: 2.0
  },
  voiceVolume: {
    type: Number,
    default: 0.8,
    min: 0.0,
    max: 1.0
  },

  // Animation Preferences
  animationEnabled: {
    type: Boolean,
    default: true
  },
  animationIntensity: {
    type: Number,
    default: 0.8,
    min: 0.1,
    max: 1.0
  },
  animationColor: {
    type: String,
    default: '#dc3545'
  },

  // Service Preferences
  preferredServices: [{
    type: String,
    enum: ['Classic Haircut', 'Beard Trim', 'Hot Towel Shave', 'Hair Styling', 'Consultation', 'Full Service']
  }],
  preferredStylist: {
    type: String,
    default: 'Andre The Barber'
  },
  preferredTimeSlots: [{
    type: String,
    enum: ['morning', 'afternoon', 'evening']
  }],

  // Communication Preferences
  reminderPreference: {
    type: String,
    enum: ['none', 'day_before', 'week_before', 'both'],
    default: 'day_before'
  },
  marketingOptIn: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Appointment Schema
const appointmentSchema = new mongoose.Schema({
  clientId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'VoiceAgentClient',
    required: true,
    index: true
  },
  serviceType: {
    type: String,
    required: true,
    enum: ['Classic Haircut', 'Beard Trim', 'Hot Towel Shave', 'Hair Styling', 'Consultation', 'Full Service', 'Touch Up', 'Special Event Styling']
  },
  appointmentDate: {
    type: Date,
    required: true,
    index: true
  },
  durationMinutes: {
    type: Number,
    default: 60,
    min: 15,
    max: 240
  },
  status: {
    type: String,
    enum: ['scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show'],
    default: 'scheduled',
    index: true
  },
  stylistName: {
    type: String,
    default: 'Andre The Barber'
  },
  price: {
    type: Number,
    min: 0
  },
  tipAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  totalAmount: {
    type: Number,
    min: 0
  },
  satisfactionRating: {
    type: Number,
    min: 1,
    max: 5
  },
  clientNotes: String,
  stylistNotes: String,
  bookedVia: {
    type: String,
    enum: ['voice_agent', 'website', 'phone', 'walk_in', 'social_media'],
    default: 'voice_agent'
  }
}, {
  timestamps: true
});

// Conversation Schema
const conversationSchema = new mongoose.Schema({
  clientId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'VoiceAgentClient',
    required: true,
    index: true
  },
  sessionId: {
    type: String,
    required: true,
    index: true
  },
  conversationType: {
    type: String,
    enum: ['welcome', 'booking', 'inquiry', 'follow_up', 'general', 'support'],
    default: 'welcome',
    index: true
  },
  triggerEvent: {
    type: String,
    enum: ['login', 'manual', 'scheduled', 'callback', 'button_click'],
    default: 'login'
  },
  conversationStart: {
    type: Date,
    default: Date.now,
    index: true
  },
  conversationEnd: Date,
  durationSeconds: Number,
  messages: [{
    timestamp: { type: Date, default: Date.now },
    speaker: { type: String, enum: ['agent', 'user'], required: true },
    content: { type: String, required: true },
    intent: String,
    confidence: { type: Number, min: 0, max: 1 },
    synthesisTimeMs: Number,
    animationSynced: { type: Boolean, default: false }
  }],
  outcome: {
    type: String,
    enum: ['completed', 'abandoned', 'transferred', 'error'],
    default: 'completed'
  },
  clientSatisfaction: {
    type: Number,
    min: 1,
    max: 5
  },
  appointmentBooked: {
    type: Boolean,
    default: false
  },
  appointmentId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'VoiceAgentAppointment'
  }
}, {
  timestamps: true
});

// Create models
const VoiceAgentClient = mongoose.model('VoiceAgentClient', clientSchema);
const VoiceAgentAppointment = mongoose.model('VoiceAgentAppointment', appointmentSchema);
const VoiceAgentConversation = mongoose.model('VoiceAgentConversation', conversationSchema);

/**
 * Initialize database with sample data
 */
async function initializeDatabase() {
  try {
    console.log('🗄️ Initializing Voice Agent database...');

    // Connect to MongoDB
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/barber-brothers';
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');

    // Create indexes
    await VoiceAgentClient.createIndexes();
    await VoiceAgentAppointment.createIndexes();
    await VoiceAgentConversation.createIndexes();
    console.log('✅ Database indexes created');

    // Check if sample data already exists
    const existingClients = await VoiceAgentClient.countDocuments();
    if (existingClients > 0) {
      console.log('📋 Sample data already exists, skipping initialization');
      return;
    }

    // Create sample client
    const sampleClient = new VoiceAgentClient({
      email: '<EMAIL>',
      googleId: 'google_123456789',
      firstName: 'John',
      lastName: 'Doe',
      displayName: 'John Doe',
      phone: '******-0123',
      totalVisits: 3,
      preferredServices: ['Classic Haircut', 'Beard Trim'],
      voiceEnabled: true,
      preferredVoice: 'en-US-Standard-A',
      voiceSpeed: 1.0,
      animationEnabled: true,
      reminderPreference: 'day_before'
    });

    await sampleClient.save();
    console.log('✅ Sample client created');

    // Create sample appointments
    const sampleAppointments = [
      {
        clientId: sampleClient._id,
        serviceType: 'Classic Haircut',
        appointmentDate: new Date('2024-06-15'),
        status: 'completed',
        price: 40,
        tipAmount: 8,
        totalAmount: 48,
        satisfactionRating: 5,
        clientNotes: 'Great cut, loved the fade',
        bookedVia: 'voice_agent'
      },
      {
        clientId: sampleClient._id,
        serviceType: 'Beard Trim',
        appointmentDate: new Date('2024-05-20'),
        status: 'completed',
        price: 25,
        tipAmount: 5,
        totalAmount: 30,
        satisfactionRating: 4,
        clientNotes: 'Good trim, maybe a bit shorter next time',
        bookedVia: 'voice_agent'
      }
    ];

    await VoiceAgentAppointment.insertMany(sampleAppointments);
    console.log('✅ Sample appointments created');

    console.log('🎉 Voice Agent database initialization completed!');

  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    throw error;
  }
}

/**
 * Export models and initialization function
 */
module.exports = {
  VoiceAgentClient,
  VoiceAgentAppointment,
  VoiceAgentConversation,
  initializeDatabase
};

// Run initialization if this file is executed directly
if (require.main === module) {
  initializeDatabase()
    .then(() => {
      console.log('Database setup completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Database setup failed:', error);
      process.exit(1);
    });
}
