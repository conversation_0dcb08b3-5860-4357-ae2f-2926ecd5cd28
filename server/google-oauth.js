require('dotenv').config();
const express = require('express');
const axios = require('axios');
const router = express.Router();

router.post('/auth/google/callback', async (req, res) => {
  const { code } = req.body;

  if (!code) {
    return res.status(400).json({ error: 'Missing code' });
  }

  try {
    console.log('🔄 Processing Google OAuth callback...');
    console.log('📋 Environment check:', {
      hasClientId: !!process.env.GOOGLE_CLIENT_ID,
      hasClientSecret: !!process.env.GOOGLE_CLIENT_SECRET,
      nodeEnv: process.env.NODE_ENV
    });

    // Determine redirect URI based on environment
    let redirectUri;
    const referer = req.get('Referer') || req.get('Origin') || '';

    if (referer.includes('localhost') || referer.includes('127.0.0.1')) {
      // Local development
      const port = referer.includes(':3000') ? ':3000' : '';
      redirectUri = `http://localhost${port}/auth-callback.html`;
    } else if (referer.includes('barberbrotherz.com')) {
      // Production
      redirectUri = 'https://www.barberbrotherz.com/auth-callback.html';
    } else {
      // Fallback - try to construct from request
      const protocol = req.secure ? 'https' : 'http';
      const host = req.get('Host') || 'localhost:3000';
      redirectUri = `${protocol}://${host}/auth-callback.html`;
    }

    console.log('🔗 Using redirect URI:', redirectUri);

    // Exchange code for tokens
    const response = await axios.post('https://oauth2.googleapis.com/token', new URLSearchParams({
        code,
        client_id: process.env.GOOGLE_CLIENT_ID,
        client_secret: process.env.GOOGLE_CLIENT_SECRET,
        redirect_uri: redirectUri,
        grant_type: 'authorization_code'
    }).toString(), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });

    const { id_token, access_token } = response.data;
    console.log('✅ Token exchange successful');

    // Optionally: verify id_token, get user info, create session, etc.
    res.json({ id_token, access_token });
  } catch (error) {
    console.error('❌ Google OAuth error:', error.response?.data || error.message);

    // Provide more detailed error information
    const errorDetails = error.response?.data || { error: error.message };
    res.status(500).json({
      error: 'Failed to exchange code for tokens',
      details: errorDetails,
      message: 'Please check your Google OAuth configuration'
    });
  }
});

module.exports = router; 