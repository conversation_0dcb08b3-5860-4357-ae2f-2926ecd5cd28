<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voice Agent Integration Test - Barber Brothers Legacy</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #fff;
            font-family: 'Roboto', sans-serif;
            min-height: 100vh;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-card {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-pending { background-color: #ffc107; }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .log-output {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .btn-test {
            background: linear-gradient(45deg, #dc3545, #ff6b6b);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            margin: 5px;
        }
        .btn-test:hover {
            background: linear-gradient(45deg, #c82333, #ff5252);
            color: white;
        }
        .voice-agent-status {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #dc3545;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="text-center mb-4">
            <h1><i class="fas fa-microphone"></i> Voice Agent Integration Test</h1>
            <p class="lead">Testing the complete AI Voice Agent integration flow</p>
        </div>

        <!-- Voice Agent Status -->
        <div class="voice-agent-status">
            <h6><i class="fas fa-robot"></i> Voice Agent Status</h6>
            <div id="voice-agent-status">
                <span class="status-indicator status-pending"></span>
                <span>Initializing...</span>
            </div>
        </div>

        <!-- Test Controls -->
        <div class="test-card">
            <h3><i class="fas fa-play-circle"></i> Test Controls</h3>
            <div class="row">
                <div class="col-md-6">
                    <button class="btn btn-test" onclick="testGoogleAuth()">
                        <i class="fas fa-sign-in-alt"></i> Test Google Auth
                    </button>
                    <button class="btn btn-test" onclick="testVoiceAgentActivation()">
                        <i class="fas fa-microphone"></i> Test Voice Activation
                    </button>
                    <button class="btn btn-test" onclick="testClientHistory()">
                        <i class="fas fa-history"></i> Test Client History
                    </button>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-test" onclick="testSpeechSynthesis()">
                        <i class="fas fa-volume-up"></i> Test Speech
                    </button>
                    <button class="btn btn-test" onclick="testAnimation()">
                        <i class="fas fa-circle"></i> Test Animation
                    </button>
                    <button class="btn btn-test" onclick="clearLogs()">
                        <i class="fas fa-trash"></i> Clear Logs
                    </button>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="row">
            <div class="col-md-6">
                <div class="test-card">
                    <h4><i class="fas fa-check-circle"></i> Test Results</h4>
                    <div id="test-results">
                        <div id="integration-status">
                            <span class="status-indicator status-pending"></span>
                            <span>Voice Agent Integration: Pending</span>
                        </div>
                        <div id="auth-status">
                            <span class="status-indicator status-pending"></span>
                            <span>Authentication: Pending</span>
                        </div>
                        <div id="api-status">
                            <span class="status-indicator status-pending"></span>
                            <span>API Endpoints: Pending</span>
                        </div>
                        <div id="speech-status">
                            <span class="status-indicator status-pending"></span>
                            <span>Speech Synthesis: Pending</span>
                        </div>
                        <div id="animation-status">
                            <span class="status-indicator status-pending"></span>
                            <span>Animation: Pending</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="test-card">
                    <h4><i class="fas fa-terminal"></i> Console Output</h4>
                    <div id="log-output" class="log-output">
                        Voice Agent Integration Test Started...\n
                    </div>
                </div>
            </div>
        </div>

        <!-- Mock User Data -->
        <div class="test-card">
            <h4><i class="fas fa-user"></i> Mock User Data</h4>
            <div class="row">
                <div class="col-md-6">
                    <label for="mock-email">Email:</label>
                    <input type="email" id="mock-email" class="form-control" value="<EMAIL>">
                </div>
                <div class="col-md-6">
                    <label for="mock-name">Name:</label>
                    <input type="text" id="mock-name" class="form-control" value="Test User">
                </div>
            </div>
            <button class="btn btn-test mt-2" onclick="simulateLogin()">
                <i class="fas fa-user-plus"></i> Simulate Login
            </button>
        </div>
    </div>

    <!-- Include Voice Agent Integration -->
    <script src="js/voice-agent-integration.js"></script>
    
    <script>
        let testLog = '';

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
            const logMessage = `[${timestamp}] ${prefix} ${message}\n`;
            testLog += logMessage;
            document.getElementById('log-output').textContent = testLog;
            document.getElementById('log-output').scrollTop = document.getElementById('log-output').scrollHeight;
            console.log(message);
        }

        function updateStatus(elementId, status, message) {
            const element = document.getElementById(elementId);
            const indicator = element.querySelector('.status-indicator');
            const text = element.querySelector('span:last-child');
            
            indicator.className = `status-indicator status-${status}`;
            text.textContent = message;
        }

        // Test Functions
        async function testGoogleAuth() {
            log('Testing Google Authentication simulation...');
            try {
                const mockUser = {
                    email: document.getElementById('mock-email').value,
                    name: document.getElementById('mock-name').value,
                    given_name: document.getElementById('mock-name').value.split(' ')[0],
                    family_name: document.getElementById('mock-name').value.split(' ')[1] || '',
                    picture: 'https://via.placeholder.com/150',
                    email_verified: true
                };

                // Simulate auth success
                localStorage.setItem('authSuccess', 'true');
                localStorage.setItem('userProfile', JSON.stringify(mockUser));
                
                // Dispatch auth event
                window.dispatchEvent(new CustomEvent('googleAuthSuccess', {
                    detail: { user: mockUser }
                }));

                updateStatus('auth-status', 'success', 'Authentication: Success');
                log('Google Authentication simulation completed', 'success');
            } catch (error) {
                updateStatus('auth-status', 'error', 'Authentication: Failed');
                log(`Authentication test failed: ${error.message}`, 'error');
            }
        }

        async function testVoiceAgentActivation() {
            log('Testing Voice Agent activation...');
            try {
                if (!window.VoiceAgentIntegration) {
                    throw new Error('Voice Agent Integration not loaded');
                }

                const bridge = window.VoiceAgentIntegration.getBridge();
                if (!bridge) {
                    throw new Error('Voice Agent Bridge not initialized');
                }

                const isActive = window.VoiceAgentIntegration.isActive();
                log(`Voice Agent active status: ${isActive}`);

                if (!isActive) {
                    await window.VoiceAgentIntegration.activate();
                }

                updateStatus('integration-status', 'success', 'Voice Agent Integration: Active');
                log('Voice Agent activation test completed', 'success');
            } catch (error) {
                updateStatus('integration-status', 'error', 'Voice Agent Integration: Failed');
                log(`Voice Agent activation failed: ${error.message}`, 'error');
            }
        }

        async function testClientHistory() {
            log('Testing Client History API...');
            try {
                const email = document.getElementById('mock-email').value;
                const response = await fetch(`/api/voice-agent/client-history/${encodeURIComponent(email)}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                log(`Client history response: ${JSON.stringify(data, null, 2)}`);

                updateStatus('api-status', 'success', 'API Endpoints: Working');
                log('Client History API test completed', 'success');
            } catch (error) {
                updateStatus('api-status', 'error', 'API Endpoints: Failed');
                log(`Client History API test failed: ${error.message}`, 'error');
            }
        }

        async function testSpeechSynthesis() {
            log('Testing Speech Synthesis...');
            try {
                if (!('speechSynthesis' in window)) {
                    throw new Error('Speech Synthesis not supported');
                }

                const utterance = new SpeechSynthesisUtterance('Hello! This is a test of the voice agent speech synthesis.');
                utterance.rate = 1.0;
                utterance.pitch = 1.0;
                utterance.volume = 0.8;

                utterance.onstart = () => log('Speech synthesis started');
                utterance.onend = () => {
                    log('Speech synthesis completed', 'success');
                    updateStatus('speech-status', 'success', 'Speech Synthesis: Working');
                };
                utterance.onerror = (error) => {
                    log(`Speech synthesis error: ${error.error}`, 'error');
                    updateStatus('speech-status', 'error', 'Speech Synthesis: Failed');
                };

                speechSynthesis.speak(utterance);
            } catch (error) {
                updateStatus('speech-status', 'error', 'Speech Synthesis: Failed');
                log(`Speech synthesis test failed: ${error.message}`, 'error');
            }
        }

        async function testAnimation() {
            log('Testing Animation System...');
            try {
                // Create a test pulsing ball
                const testBall = document.createElement('div');
                testBall.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    width: 60px;
                    height: 60px;
                    background: radial-gradient(circle, #dc3545, #ff6b6b);
                    border-radius: 50%;
                    transform: translate(-50%, -50%);
                    animation: pulse 1s ease-in-out infinite;
                    z-index: 9999;
                `;

                // Add CSS animation
                const style = document.createElement('style');
                style.textContent = `
                    @keyframes pulse {
                        0%, 100% { transform: translate(-50%, -50%) scale(1); }
                        50% { transform: translate(-50%, -50%) scale(1.2); }
                    }
                `;
                document.head.appendChild(style);
                document.body.appendChild(testBall);

                // Remove after 3 seconds
                setTimeout(() => {
                    document.body.removeChild(testBall);
                    document.head.removeChild(style);
                }, 3000);

                updateStatus('animation-status', 'success', 'Animation: Working');
                log('Animation test completed', 'success');
            } catch (error) {
                updateStatus('animation-status', 'error', 'Animation: Failed');
                log(`Animation test failed: ${error.message}`, 'error');
            }
        }

        function simulateLogin() {
            log('Simulating complete login flow...');
            testGoogleAuth().then(() => {
                setTimeout(() => {
                    testVoiceAgentActivation();
                }, 1000);
            });
        }

        function clearLogs() {
            testLog = 'Voice Agent Integration Test Cleared...\n';
            document.getElementById('log-output').textContent = testLog;
        }

        // Initialize test page
        document.addEventListener('DOMContentLoaded', () => {
            log('Voice Agent Integration Test Page Loaded');
            
            // Listen for voice agent events
            window.addEventListener('voiceAgentIntegrationReady', (event) => {
                log('Voice Agent Integration Ready event received', 'success');
                updateStatus('voice-agent-status', 'success', 'Ready');
                document.querySelector('.voice-agent-status span:last-child').textContent = 'Ready';
            });

            window.addEventListener('voiceAgentIntegrationError', (event) => {
                log(`Voice Agent Integration Error: ${event.detail.error}`, 'error');
                updateStatus('voice-agent-status', 'error', 'Error');
                document.querySelector('.voice-agent-status span:last-child').textContent = 'Error';
            });

            window.addEventListener('voiceAgentReady', (event) => {
                log('Voice Agent Ready event received', 'success');
                updateStatus('integration-status', 'success', 'Voice Agent Integration: Ready');
            });

            // Auto-test API endpoints
            setTimeout(() => {
                testClientHistory();
            }, 2000);
        });
    </script>
</body>
</html>
