// Google OAuth Configuration
// This file contains the configuration for Google Sign-In

const GOOGLE_AUTH_CONFIG = {
    // Google OAuth Client ID for Barber Brothers Legacy
    // SECURITY UPDATE: Using secure client-side OAuth configuration
    clientId: '902371497891-ub0nfb5iu5ke57pi13vqilrdp74ktt0v.apps.googleusercontent.com',

    // Scopes to request from Google (minimal required scopes)
    scopes: [
        'openid',
        'email',
        'profile'
    ],

    // Redirect URI configuration for production domain
    get redirectUri() {
        const origin = window.location.origin;
        const hostname = window.location.hostname;

        console.log('🔗 Determining redirect URI for origin:', origin);

        // For production domain
        if (hostname === 'www.barberbrotherz.com' || hostname === 'barberbrotherz.com') {
            return 'https://www.barberbrotherz.com/auth-callback.html';
        }

        // For local development (localhost or 127.0.0.1)
        if (hostname === 'localhost' || hostname === '127.0.0.1') {
            return origin + '/auth-callback.html';
        }

        // For other development environments (e.g., Netlify, Vercel)
        if (origin.startsWith('https://')) {
            return origin + '/auth-callback.html';
        }

        // Default fallback for HTTP (local development)
        return origin + '/auth-callback.html';
    },

    // SECURITY: Use authorization code flow (more secure)
    responseType: 'code',

    // Access type for refresh tokens
    accessType: 'online', // Changed from 'offline' for better security

    // Prompt parameter - force account selection for security
    prompt: 'select_account',

    // Additional security parameters
    include_granted_scopes: true,
    enable_granular_consent: true,

    // Security state parameter
    get state() {
        return {
            timestamp: Date.now(),
            nonce: Math.random().toString(36).substring(2, 15),
            origin: window.location.origin
        };
    }
};

// SECURITY UPDATE: Enhanced OAuth URL generation with better security
function getGoogleAuthUrl(source) {
    try {
        // Create secure state parameter with multiple security checks
        const stateData = {
            source: source,
            timestamp: Date.now(),
            nonce: Math.random().toString(36).substring(2, 15),
            origin: window.location.origin,
            // Add CSRF protection
            csrf: btoa(Math.random().toString()).substring(0, 12)
        };

        const state = encodeURIComponent(JSON.stringify(stateData));

        // Validate configuration before creating URL
        if (!GOOGLE_AUTH_CONFIG.clientId) {
            throw new Error('Google Client ID not configured');
        }

        const redirectUri = GOOGLE_AUTH_CONFIG.redirectUri;
        console.log('🔗 Using redirect URI:', redirectUri);

        // Build OAuth parameters with enhanced security
        const params = new URLSearchParams({
            client_id: GOOGLE_AUTH_CONFIG.clientId,
            redirect_uri: redirectUri,
            scope: GOOGLE_AUTH_CONFIG.scopes.join(' '),
            response_type: GOOGLE_AUTH_CONFIG.responseType,
            state: state,
            access_type: GOOGLE_AUTH_CONFIG.accessType,
            prompt: GOOGLE_AUTH_CONFIG.prompt,
            // Add additional security parameters
            include_granted_scopes: GOOGLE_AUTH_CONFIG.include_granted_scopes,
            enable_granular_consent: GOOGLE_AUTH_CONFIG.enable_granular_consent
        });

        const authUrl = `https://accounts.google.com/o/oauth2/v2/auth?${params.toString()}`;

        // Log for debugging (remove in production)
        console.log('🔐 Generated secure OAuth URL for source:', source);
        console.log('📋 OAuth parameters:', Object.fromEntries(params));

        return authUrl;

    } catch (error) {
        console.error('❌ Error generating OAuth URL:', error);
        throw new Error(`Failed to generate OAuth URL: ${error.message}`);
    }
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { GOOGLE_AUTH_CONFIG, getGoogleAuthUrl };
} else {
    window.GOOGLE_AUTH_CONFIG = GOOGLE_AUTH_CONFIG;
    window.getGoogleAuthUrl = getGoogleAuthUrl;
}
